package com.yixun.wid.v2.vo.ai;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.List;

/**
 * 材料分类响应结果
 */
@Data
public class MaterialClassificationResponse {

    /**
     * 状态
     */
    private String status;

    /**
     * 分类数据
     */
    private ClassificationData data;

    /**
     * 消息
     */
    private String message;

    @Data
    public static class ClassificationData {
        /**
         * 申请材料
         */
        private ApplicationMaterials application;

        /**
         * 就诊材料
         */
        private MedicalMaterials medical;
    }

    @Data
    public static class ApplicationMaterials {
        /**
         * 工伤待遇申请表
         */
        @JSONField(name = "work_injury_benefit_application_form")
        private List<String> workInjuryBenefitApplicationForm;

        /**
         * 工伤医疗(康复)待遇申请表
         */
        @JSONField(name = "work_injury_medical_rehabilitation_benefit_application_form")
        private List<String> workInjuryMedicalRehabilitationBenefitApplicationForm;

        /**
         * 工伤决定书
         */
        @JSONField(name = "work_injury_determination_document")
        private List<String> workInjuryDeterminationDocument;

        /**
         * 工伤收件清单
         */
        @JSONField(name = "work_injury_receipt_list")
        private List<String> workInjuryReceiptList;

        /**
         * 身份证
         */
        @JSONField(name = "id_card")
        private List<String> idCard;

        /**
         * 社会保障卡
         */
        @JSONField(name = "social_security_card")
        private List<String> socialSecurityCard;

        /**
         * 银行卡
         */
        @JSONField(name = "bank_card")
        private List<String> bankCard;

        /**
         * 初次(复查)鉴定结论书
         */
        @JSONField(name = "initial_reexamination_appraisal_conclusion_document")
        private List<String> initialReexaminationAppraisalConclusionDocument;

        /**
         * 非税收入一般缴款书
         */
        @JSONField(name = "non_tax_revenue_general_payment_document")
        private List<String> nonTaxRevenueGeneralPaymentDocument;

        /**
         * 其他
         */
        private List<OtherMaterial> other;
    }
    
    @Data
    public static class MedicalMaterials {
        /**
         * 病历（大类）
         */
        @JSONField(name = "medical_record")
        private MedicalRecord medicalRecord;

        /**
         * 手术记录
         */
        @JSONField(name = "surgical_record")
        private List<String> surgicalRecord;

        /**
         * 合格证
         */
        private List<String> certificate;

        /**
         * 清单
         */
        private List<String> list;

        /**
         * 电子发票
         */
        @JSONField(name = "electronic_invoice")
        private List<String> electronicInvoice;

        /**
         * 非电子发票
         */
        @JSONField(name = "non_electronic_invoice")
        private List<String> nonElectronicInvoice;

        /**
         * 追回单
         */
        @JSONField(name = "recall_notice")
        private List<String> recallNotice;

        /**
         * 检查报告
         */
        @JSONField(name = "examination_report")
        private List<String> examinationReport;

        /**
         * 医嘱单
         */
        @JSONField(name = "doctor_order_sheet")
        private List<String> doctorOrderSheet;

        /**
         * 其他就诊报告
         */
        @JSONField(name = "other_consultation_reports")
        private List<String> otherConsultationReports;

        /**
         * 其他未识别
         */
        @JSONField(name = "other_unidentified")
        private List<String> otherUnidentified;
    }

    @Data
    public static class MedicalRecord {
        /**
         * 病历小类，如果修改文件类型为病历就添加到这里
         */
        @JSONField(name = "medical_record_subcategory")
        private List<String> medicalRecordSubcategory;

        /**
         * 病情证明书
         */
        @JSONField(name = "medical_condition_certificate")
        private List<String> medicalConditionCertificate;

        /**
         * 出院证明书
         */
        @JSONField(name = "discharge_certificate")
        private List<String> dischargeCertificate;

        /**
         * 出院记录
         */
        @JSONField(name = "discharge_record")
        private List<String> dischargeRecord;

        /**
         * 住院病案首页
         */
        @JSONField(name = "inpatient_medical_record_first_page")
        private List<String> inpatientMedicalRecordFirstPage;

        /**
         * 诊断证明书
         */
        @JSONField(name = "diagnosis_certificate")
        private List<String> diagnosisCertificate;

        /**
         * 入院证
         */
        @JSONField(name = "admission_certificate")
        private List<String> admissionCertificate;

        /**
         * 入院记录
         */
        @JSONField(name = "admission_record")
        private List<String> admissionRecord;

        /**
         * 处方签(笺)
         */
        @JSONField(name = "prescription_slip")
        private List<String> prescriptionSlip;

        /**
         * 体温单
         */
        @JSONField(name = "temperature_sheet")
        private List<String> temperatureSheet;

        /**
         * 病程记录
         */
        @JSONField(name = "progress_note")
        private List<String> progressNote;
    }

    @Data
    public static class OtherMaterial {
        /**
         * 类型名称
         */
        @JSONField(name = "type_name")
        private String typeName;

        /**
         * 文件
         */
        private String file;
    }
}
