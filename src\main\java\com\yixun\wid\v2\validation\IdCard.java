package com.yixun.wid.v2.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 身份证号码校验注解
 * 支持15位和18位身份证号码校验
 * 允许为空值
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = IdCardValidator.class)
public @interface IdCard {
    
    /**
     * 校验失败时的错误消息
     */
    String message() default "身份证号码格式不正确";
    
    /**
     * 校验分组
     */
    Class<?>[] groups() default {};
    
    /**
     * 负载
     */
    Class<? extends Payload>[] payload() default {};
}
