package com.yixun.wid.v2.bean.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 检查医院性质请求参数
 */
@Data
public class CheckHospitalNatureIn {

    /**
     * 医疗机构ID
     */
    @NotNull(message = "医疗机构ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long medicalInstitutionId;

    /**
     * 门诊开始时间/入院日期
     */
    @NotNull(message = "门诊开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date outpatientStartTime;
}
