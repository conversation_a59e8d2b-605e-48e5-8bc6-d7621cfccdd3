package com.yixun.wid.v2.controller;

import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.v2.entity.LegalRegulation;
import com.yixun.wid.v2.service.LegalRegulationService;
import com.yixun.wid.v2.vo.SortVO;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 法规依据管理接口
 */
@RequestMapping("/v2/legal/regulation")
@RestController
@AllArgsConstructor
public class LegalRegulationController {

    private final LegalRegulationService legalRegulationService;

    /**
     * 新增法规依据
     *
     * @param legalRegulation 法规依据
     */
    @PostMapping("/save")
    public CommonResult<Void> save(@Valid @RequestBody LegalRegulation legalRegulation) {
        legalRegulationService.save(legalRegulation);
        return CommonResult.successResult("操作成功");
    }

    /**
     * 查询法规依据列表
     *
     * @param status     状态
     * @param search     搜索关键词（法规名称）
     * @param commonPage 分页参数
     * @return 法规依据列表
     */
    @GetMapping("/list")
    public CommonResult<List<LegalRegulation>> list(
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String search,
            CommonPage commonPage) {

        List<LegalRegulation> legalRegulations = legalRegulationService.list(status, search, commonPage);
        return CommonResult.successPageData(legalRegulations, commonPage);
    }

    /**
     * 查询法规依据列表（不分页）
     *
     * @param status  状态
     * @param search  搜索关键词
     * @return 法规依据列表
     */
    @GetMapping("/listAll")
    public CommonResult<List<LegalRegulation>> listAll(
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String search) {

        List<LegalRegulation> legalRegulations = legalRegulationService.listAll(status, search);
        return CommonResult.successData(legalRegulations);
    }

    /**
     * 根据id查询法规依据
     *
     * @param id 主键
     * @return 法规依据
     */
    @GetMapping
    public CommonResult<LegalRegulation> getById(@RequestParam("id") Long id) {
        LegalRegulation legalRegulation = legalRegulationService.getById(id);
        return CommonResult.successData(legalRegulation);
    }

    /**
     * 更新法规依据
     *
     * @param legalRegulation 法规依据
     * @return 更新结果
     */
    @PostMapping("/update")
    public CommonResult<Void> update(@Valid @RequestBody LegalRegulation legalRegulation) {
        legalRegulationService.update(legalRegulation);
        return CommonResult.successResult("操作成功");
    }

    /**
     * 批量删除法规依据
     *
     * @param ids 法规依据ID列表
     * @return 删除结果
     */
    @PostMapping("/batchDelete")
    public CommonResult<Void> batchDelete(@RequestBody List<Long> ids) {
        legalRegulationService.batchDelete(ids);
        return CommonResult.successResult("删除成功");
    }

    /**
     * 更新法规依据列表排序
     *
     * @param sort 列表排序
     * @return 更新结果
     */
    @PostMapping("/update/sort")
    public CommonResult<Void> updateSort(@RequestBody List<SortVO> sort) {
        legalRegulationService.updateSort(sort);
        return CommonResult.successResult("操作成功");
    }

    /**
     * 智能搜索法规依据
     *
     * @param regulationName 法规名称（可选）
     * @param content        条款内容（可选）
     * @return 法规依据列表
     */
    @GetMapping("/smartSearch")
    public CommonResult<List<LegalRegulation>> smartSearch(
            @RequestParam(required = false) String regulationName,
            @RequestParam(required = false) String content) {

        List<LegalRegulation> regulations = legalRegulationService.smartSearch(regulationName, content);
        return CommonResult.successData(regulations);
    }
}
