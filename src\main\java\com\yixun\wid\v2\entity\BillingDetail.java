package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yixun.wid.v2.service.PriceLimitCheckService;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 电子清单实体
 */
@Data
@Document("billingDetail")
public class BillingDetail {

    /**
     * 主键id
     */
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    
    /**
     * 关联的账单信息ID，多对一关系
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long billingInfoId;
    
    /**
     * 明细在当前账单中的序号
     */
    private Integer orderNum;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 项目编码
     */
    private String projectCode;
    
    /**
     * 费用类别
     */
    private String feeType;
    
    /**
     * 费用分类
     */
    private String feeCategory;
    
    /**
     * 费用等级
     */
    private String feeLevel;

    /**
     * 三目录ID（冗余字段）
     * 从三目录相似搜索结果中冗余过来，便于关联查询
     */
    private String threeCatalogueId;

    /**
     * 数量
     */
    private Integer quantity;
    
    /**
     * 单价(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer unitPriceInCent;
    
    /**
     * 单价(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal unitPrice;
    
    /**
     * 金额(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer amountInCent;
    
    /**
     * 金额(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal amount;
    
    /**
     * 是否工伤
     */
    private Boolean isWorkInjury;
    
    /**
     * 不可报销金额(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer nonReimbursableAmountInCent;
    
    /**
     * 不可报销金额(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal nonReimbursableAmount;

    /**
     * 扣减类型（非工伤扣减，审核扣减）
     */
    private String deductionType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;

    /**
     * 计算异常状态
     * 当数量*单价≠金额时，标记为异常
     */
    @Transient
    private Boolean calculationError;
    
    /**
     * 获取单价(元)
     * 将数据库中存储的分转换为元
     */
    public BigDecimal getUnitPrice() {
        if (unitPriceInCent == null) {
            return null;
        }
        return new BigDecimal(unitPriceInCent).divide(new BigDecimal(100));
    }
    
    /**
     * 设置单价(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setUnitPrice(BigDecimal unitPrice) {
        if (unitPrice == null) {
            this.unitPriceInCent = null;
            return;
        }
        this.unitPriceInCent = unitPrice.multiply(new BigDecimal(100)).intValue();
    }
    
    /**
     * 获取金额(元)
     * 将数据库中存储的分转换为元
     */
    public BigDecimal getAmount() {
        if (amountInCent == null) {
            return null;
        }
        return new BigDecimal(amountInCent).divide(new BigDecimal(100));
    }
    
    /**
     * 设置金额(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setAmount(BigDecimal amount) {
        if (amount == null) {
            this.amountInCent = null;
            return;
        }
        this.amountInCent = amount.multiply(new BigDecimal(100)).intValue();
    }
    
    /**
     * 获取不可报销金额(元)
     * 将数据库中存储的分转换为元，默认为0
     */
    public BigDecimal getNonReimbursableAmount() {
        if (nonReimbursableAmountInCent == null) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(nonReimbursableAmountInCent).divide(new BigDecimal(100));
    }
    
    /**
     * 设置不可报销金额(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setNonReimbursableAmount(BigDecimal nonReimbursableAmount) {
        if (nonReimbursableAmount == null) {
            this.nonReimbursableAmountInCent = null;
            return;
        }
        this.nonReimbursableAmountInCent = nonReimbursableAmount.multiply(new BigDecimal(100)).intValue();
    }

    /**
     * 自动计算金额、数量、单价中缺失的值
     * 基于公式：金额 = 数量 * 单价
     */
    public void autoCalculate() {
        // 如果三个值都有，检查是否计算正确
        if (quantity != null && unitPrice != null && amount != null) {
            BigDecimal calculatedAmount = unitPrice.multiply(new BigDecimal(quantity));
            // 使用compareTo比较BigDecimal，避免精度问题
            if (calculatedAmount.compareTo(amount) != 0) {
                this.calculationError = true;
                return;
            } else {
                this.calculationError = false;
                return;
            }
        }

        // 重置异常状态
        this.calculationError = false;

        // 如果有数量和单价，计算金额
        if (quantity != null && unitPrice != null && amount == null) {
            BigDecimal calculatedAmount = unitPrice.multiply(new BigDecimal(quantity));
            this.setAmount(calculatedAmount);
            return;
        }

        // 如果有金额和单价，计算数量
        if (amount != null && unitPrice != null && quantity == null) {
            if (unitPrice.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal calculatedQuantity = amount.divide(unitPrice, 0, BigDecimal.ROUND_HALF_UP);
                this.quantity = calculatedQuantity.intValue();
            }
            return;
        }

        // 如果有金额和数量，计算单价
        if (amount != null && quantity != null && unitPrice == null) {
            if (quantity != 0) {
                BigDecimal calculatedUnitPrice = amount.divide(new BigDecimal(quantity), 2, BigDecimal.ROUND_HALF_UP);
                this.setUnitPrice(calculatedUnitPrice);
            }
            return;
        }

        // 处理非工伤情况：当是否工伤为否时，设置不可报销金额和扣减类型为非工伤扣减
        if (Boolean.FALSE.equals(isWorkInjury) && amount != null) {
            this.setNonReimbursableAmount(amount);
            this.deductionType = "非工伤扣减";
        }

        // 处理丙类费用：当是否工伤为是且费用等级为丙时，设置不可报销金额和扣减类型为审核扣减
        if (Boolean.TRUE.equals(isWorkInjury) && "丙".equals(feeLevel) && amount != null) {
            this.setNonReimbursableAmount(amount);
            this.deductionType = "审核扣减";
        }

        // 处理甲类费用限价检查：当满足条件时，检查是否超出限价
        if (shouldCheckPriceLimit()) {
            try {
                // 使用hutool的SpringUtil获取PriceLimitCheckService实例
                PriceLimitCheckService priceLimitCheckService = SpringUtil.getBean(PriceLimitCheckService.class);
                if (priceLimitCheckService != null) {
                    // 调用限价检查方法
                    priceLimitCheckService.checkAndSetPriceLimitDeduction(this);
                }
            } catch (Exception e) {
                // 限价检查异常不影响主流程，静默处理
                // 在实体类中不建议使用日志，这里只是静默处理异常
            }
        }
    }

    /**
     * 检查是否应该进行限价检查
     *
     * @return true表示需要进行限价检查，false表示不需要
     */
    public boolean shouldCheckPriceLimit() {
        return Boolean.TRUE.equals(isWorkInjury) &&
               "甲".equals(feeLevel) &&
               billingInfoId != null &&
               StrUtil.isNotBlank(threeCatalogueId) &&
               unitPrice != null &&
               quantity != null;
    }





    /**
     * 获取计算异常状态
     * 在查询时动态计算是否存在计算异常
     */
    public Boolean getCalculationError() {
        if (quantity != null && unitPrice != null && amount != null) {
            BigDecimal calculatedAmount = unitPrice.multiply(new BigDecimal(quantity));
            return calculatedAmount.compareTo(amount) != 0;
        }
        return false;
    }
}