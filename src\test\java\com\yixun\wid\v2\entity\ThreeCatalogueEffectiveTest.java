package com.yixun.wid.v2.entity;

import org.junit.jupiter.api.Test;

import java.util.Calendar;
import java.util.Date;

/**
 * ThreeCatalogue有效性测试类
 */
public class ThreeCatalogueEffectiveTest {

    /**
     * 获取指定天数前/后的日期
     * @param days 天数，正数表示未来，负数表示过去
     * @return 日期
     */
    private Date getDateByDays(int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return calendar.getTime();
    }

    @Test
    public void testEffectiveWithNoDates() {
        // 测试：既无开始日期也无结束日期（永久有效）
        ThreeCatalogue catalogue = new ThreeCatalogue();
        catalogue.setProjectName("测试项目");
        catalogue.setLevel("甲");
        catalogue.setType("药品");
        // 不设置开始和结束日期
        
        assert catalogue.getEffective() == true;
        System.out.println("测试通过：既无开始日期也无结束日期 - 有效");
    }

    @Test
    public void testEffectiveWithOnlyStartDate() {
        // 测试：只有开始日期
        ThreeCatalogue catalogue = new ThreeCatalogue();
        catalogue.setProjectName("测试项目");
        catalogue.setLevel("甲");
        catalogue.setType("药品");
        
        // 情况1：开始日期在过去（应该有效）
        catalogue.setStartDate(getDateByDays(-10)); // 10天前
        assert catalogue.getEffective() == true;
        System.out.println("测试通过：开始日期在过去 - 有效");
        
        // 情况2：开始日期是今天（应该有效）
        catalogue.setStartDate(new Date());
        assert catalogue.getEffective() == true;
        System.out.println("测试通过：开始日期是今天 - 有效");
        
        // 情况3：开始日期在未来（应该无效）
        catalogue.setStartDate(getDateByDays(10)); // 10天后
        assert catalogue.getEffective() == false;
        System.out.println("测试通过：开始日期在未来 - 无效");
    }

    @Test
    public void testEffectiveWithOnlyEndDate() {
        // 测试：只有结束日期
        ThreeCatalogue catalogue = new ThreeCatalogue();
        catalogue.setProjectName("测试项目");
        catalogue.setLevel("甲");
        catalogue.setType("药品");
        
        // 情况1：结束日期在未来（应该有效）
        catalogue.setEndDate(getDateByDays(10)); // 10天后
        assert catalogue.getEffective() == true;
        System.out.println("测试通过：结束日期在未来 - 有效");
        
        // 情况2：结束日期是今天（应该有效）
        catalogue.setEndDate(new Date());
        assert catalogue.getEffective() == true;
        System.out.println("测试通过：结束日期是今天 - 有效");
        
        // 情况3：结束日期在过去（应该无效）
        catalogue.setEndDate(getDateByDays(-10)); // 10天前
        assert catalogue.getEffective() == false;
        System.out.println("测试通过：结束日期在过去 - 无效");
    }

    @Test
    public void testEffectiveWithBothDates() {
        // 测试：同时有开始日期和结束日期
        ThreeCatalogue catalogue = new ThreeCatalogue();
        catalogue.setProjectName("测试项目");
        catalogue.setLevel("甲");
        catalogue.setType("药品");
        
        // 情况1：当前日期在有效期内（应该有效）
        catalogue.setStartDate(getDateByDays(-10)); // 10天前开始
        catalogue.setEndDate(getDateByDays(10));    // 10天后结束
        assert catalogue.getEffective() == true;
        System.out.println("测试通过：当前日期在有效期内 - 有效");
        
        // 情况2：当前日期等于开始日期（应该有效）
        catalogue.setStartDate(new Date());         // 今天开始
        catalogue.setEndDate(getDateByDays(10));    // 10天后结束
        assert catalogue.getEffective() == true;
        System.out.println("测试通过：当前日期等于开始日期 - 有效");
        
        // 情况3：当前日期等于结束日期（应该有效）
        catalogue.setStartDate(getDateByDays(-10)); // 10天前开始
        catalogue.setEndDate(new Date());           // 今天结束
        assert catalogue.getEffective() == true;
        System.out.println("测试通过：当前日期等于结束日期 - 有效");
        
        // 情况4：当前日期在开始日期之前（应该无效）
        catalogue.setStartDate(getDateByDays(5));   // 5天后开始
        catalogue.setEndDate(getDateByDays(15));    // 15天后结束
        assert catalogue.getEffective() == false;
        System.out.println("测试通过：当前日期在开始日期之前 - 无效");
        
        // 情况5：当前日期在结束日期之后（应该无效）
        catalogue.setStartDate(getDateByDays(-20)); // 20天前开始
        catalogue.setEndDate(getDateByDays(-5));    // 5天前结束
        assert catalogue.getEffective() == false;
        System.out.println("测试通过：当前日期在结束日期之后 - 无效");
    }

    @Test
    public void testEffectiveWithInvalidDateRange() {
        // 测试：开始日期晚于结束日期的情况（虽然业务逻辑会阻止这种情况，但测试逻辑的健壮性）
        ThreeCatalogue catalogue = new ThreeCatalogue();
        catalogue.setProjectName("测试项目");
        catalogue.setLevel("甲");
        catalogue.setType("药品");
        
        // 开始日期晚于结束日期
        catalogue.setStartDate(getDateByDays(10));  // 10天后开始
        catalogue.setEndDate(getDateByDays(5));     // 5天后结束
        
        // 在这种情况下，逻辑应该返回false（无效）
        assert catalogue.getEffective() == false;
        System.out.println("测试通过：开始日期晚于结束日期 - 无效");
    }

    @Test
    public void testEffectiveEdgeCases() {
        // 测试边界情况
        ThreeCatalogue catalogue = new ThreeCatalogue();
        catalogue.setProjectName("测试项目");
        catalogue.setLevel("甲");
        catalogue.setType("药品");
        
        // 情况1：开始日期和结束日期相同（当天有效）
        Date today = new Date();
        catalogue.setStartDate(today);
        catalogue.setEndDate(today);
        assert catalogue.getEffective() == true;
        System.out.println("测试通过：开始日期和结束日期相同（当天） - 有效");
        
        // 情况2：开始日期和结束日期都在过去的同一天
        Date pastDate = getDateByDays(-5);
        catalogue.setStartDate(pastDate);
        catalogue.setEndDate(pastDate);
        assert catalogue.getEffective() == false;
        System.out.println("测试通过：开始日期和结束日期都在过去的同一天 - 无效");
        
        // 情况3：开始日期和结束日期都在未来的同一天
        Date futureDate = getDateByDays(5);
        catalogue.setStartDate(futureDate);
        catalogue.setEndDate(futureDate);
        assert catalogue.getEffective() == false;
        System.out.println("测试通过：开始日期和结束日期都在未来的同一天 - 无效");
    }

    @Test
    public void testEffectiveBusinessScenarios() {
        // 测试实际业务场景
        System.out.println("=== 业务场景测试 ===");
        
        // 场景1：长期有效的药品目录
        ThreeCatalogue longTermDrug = new ThreeCatalogue();
        longTermDrug.setProjectName("阿司匹林");
        longTermDrug.setLevel("甲");
        longTermDrug.setType("药品");
        longTermDrug.setStartDate(getDateByDays(-365)); // 一年前开始
        // 无结束日期，长期有效
        assert longTermDrug.getEffective() == true;
        System.out.println("场景1通过：长期有效的药品目录 - 有效");
        
        // 场景2：临时医疗项目
        ThreeCatalogue temporaryProject = new ThreeCatalogue();
        temporaryProject.setProjectName("临时检查项目");
        temporaryProject.setLevel("乙");
        temporaryProject.setType("诊疗");
        temporaryProject.setStartDate(getDateByDays(-30)); // 30天前开始
        temporaryProject.setEndDate(getDateByDays(30));    // 30天后结束
        assert temporaryProject.getEffective() == true;
        System.out.println("场景2通过：临时医疗项目 - 有效");
        
        // 场景3：已过期的医疗器械
        ThreeCatalogue expiredDevice = new ThreeCatalogue();
        expiredDevice.setProjectName("过期医疗器械");
        expiredDevice.setLevel("丙");
        expiredDevice.setType("医疗器械");
        expiredDevice.setStartDate(getDateByDays(-100)); // 100天前开始
        expiredDevice.setEndDate(getDateByDays(-10));    // 10天前结束
        assert expiredDevice.getEffective() == false;
        System.out.println("场景3通过：已过期的医疗器械 - 无效");
        
        // 场景4：未来生效的新药品
        ThreeCatalogue futureNewDrug = new ThreeCatalogue();
        futureNewDrug.setProjectName("未来新药品");
        futureNewDrug.setLevel("甲");
        futureNewDrug.setType("药品");
        futureNewDrug.setStartDate(getDateByDays(30));  // 30天后开始
        futureNewDrug.setEndDate(getDateByDays(365));   // 一年后结束
        assert futureNewDrug.getEffective() == false;
        System.out.println("场景4通过：未来生效的新药品 - 无效");
    }
}
