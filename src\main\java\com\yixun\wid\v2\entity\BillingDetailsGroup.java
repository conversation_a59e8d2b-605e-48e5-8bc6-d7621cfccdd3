package com.yixun.wid.v2.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.data.annotation.Transient;

import java.math.BigDecimal;
import java.util.List;

/**
 * 电子清单分组实体
 */
@Data
public class BillingDetailsGroup {

    /**
     * 编码
     */
    private String code;

    /**
     * 费用项目（费用类别）
     */
    private String feeType;

    /**
     * 账单金额(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer billAmountInCent;

    /**
     * 账单金额(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal billAmount;

    /**
     * 金额明细，由哪些金额求和
     */
    private List<BigDecimal> amountList;

    /**
     * 合理费用(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer reasonableFeeInCent;

    /**
     * 合理费用(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal reasonableFee;

    /**
     * 审核扣减(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer auditDeductionInCent;

    /**
     * 审核扣减(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal auditDeduction;

    /**
     * 非工伤扣减(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer nonWorkInjuryDeductionInCent;

    /**
     * 非工伤扣减(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal nonWorkInjuryDeduction;

    /**
     * 备注
     */
    private String remark;

    /**
     * 获取账单金额(元)
     * 将数据库中存储的分转换为元
     */
    public BigDecimal getBillAmount() {
        if (billAmountInCent == null) {
            return null;
        }
        return new BigDecimal(billAmountInCent).divide(new BigDecimal(100));
    }

    /**
     * 设置账单金额(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setBillAmount(BigDecimal billAmount) {
        if (billAmount == null) {
            this.billAmountInCent = null;
            return;
        }
        this.billAmountInCent = billAmount.multiply(new BigDecimal(100)).intValue();
    }

    /**
     * 获取合理费用(元)
     * 将数据库中存储的分转换为元
     */
    public BigDecimal getReasonableFee() {
        if (reasonableFeeInCent == null) {
            return null;
        }
        return new BigDecimal(reasonableFeeInCent).divide(new BigDecimal(100));
    }

    /**
     * 设置合理费用(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setReasonableFee(BigDecimal reasonableFee) {
        if (reasonableFee == null) {
            this.reasonableFeeInCent = null;
            return;
        }
        this.reasonableFeeInCent = reasonableFee.multiply(new BigDecimal(100)).intValue();
    }

    /**
     * 获取审核扣减(元)
     * 将数据库中存储的分转换为元
     */
    public BigDecimal getAuditDeduction() {
        if (auditDeductionInCent == null) {
            return null;
        }
        return new BigDecimal(auditDeductionInCent).divide(new BigDecimal(100));
    }

    /**
     * 设置审核扣减(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setAuditDeduction(BigDecimal auditDeduction) {
        if (auditDeduction == null) {
            this.auditDeductionInCent = null;
            return;
        }
        this.auditDeductionInCent = auditDeduction.multiply(new BigDecimal(100)).intValue();
    }

    /**
     * 获取非工伤扣减(元)
     * 将数据库中存储的分转换为元
     */
    public BigDecimal getNonWorkInjuryDeduction() {
        if (nonWorkInjuryDeductionInCent == null) {
            return null;
        }
        return new BigDecimal(nonWorkInjuryDeductionInCent).divide(new BigDecimal(100));
    }

    /**
     * 设置非工伤扣减(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setNonWorkInjuryDeduction(BigDecimal nonWorkInjuryDeduction) {
        if (nonWorkInjuryDeduction == null) {
            this.nonWorkInjuryDeductionInCent = null;
            return;
        }
        this.nonWorkInjuryDeductionInCent = nonWorkInjuryDeduction.multiply(new BigDecimal(100)).intValue();
    }
}
