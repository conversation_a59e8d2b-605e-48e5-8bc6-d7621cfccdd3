//package com.yixun.wid.v2.controller;
//
//import com.yixun.bean.CommonResult;
//import com.yixun.wid.v2.utils.AiUtils;
//import com.yixun.wid.v2.vo.ai.BillInformationResponse;
//import com.yixun.wid.v2.vo.ai.BillOcrRequest;
//import com.yixun.wid.v2.vo.ai.ListOcrRequest;
//import com.yixun.wid.v2.vo.ai.ListOcrResponse;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.anyString;
//import static org.mockito.Mockito.when;
//
///**
// * MedicalCasesController OCR接口测试
// */
//@ExtendWith(MockitoExtension.class)
//public class MedicalCasesControllerOcrTest {
//
//    @Mock
//    private AiUtils aiUtils;
//
//    @InjectMocks
//    private MedicalCasesController medicalCasesController;
//
//    @BeforeEach
//    public void setUp() {
//        // 初始化测试环境
//    }
//
//    @Test
//    public void testBillOcrWithValidUrl() {
//        // 准备测试数据
//        String fileUrl = "https://example.com/bill.pdf";
//        BillOcrRequest request = new BillOcrRequest();
//        request.setFile(fileUrl);
//
//        BillInformationResponse mockResponse = new BillInformationResponse();
//        mockResponse.setStatus("success");
//        mockResponse.setMessage("识别成功");
//
//        // 模拟AiUtils的返回
//        when(aiUtils.billOcr(anyString())).thenReturn(mockResponse);
//
//        // 执行测试
//        CommonResult<BillInformationResponse> result = medicalCasesController.billOcr(request);
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(Integer.valueOf(200), result.getCode());
//        assertNotNull(result.getData());
//        assertEquals("success", result.getData().getStatus());
//        assertEquals("识别成功", result.getData().getMessage());
//    }
//
//    @Test
//    public void testBillOcrWithEmptyUrl() {
//        // 执行测试 - 空URL
//        BillOcrRequest request = new BillOcrRequest();
//        request.setFile("");
//        CommonResult<BillInformationResponse> result = medicalCasesController.billOcr(request);
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(Integer.valueOf(1001), result.getCode());
//        assertEquals("文件URL不能为空", result.getMsg());
//    }
//
//    @Test
//    public void testBillOcrWithNullUrl() {
//        // 执行测试 - null URL
//        CommonResult<BillInformationResponse> result = medicalCasesController.billOcr(null);
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(Integer.valueOf(1001), result.getCode());
//        assertEquals("文件URL不能为空", result.getMsg());
//    }
//
//    @Test
//    public void testBillOcrWithNullFileInRequest() {
//        // 执行测试 - request中file为null
//        BillOcrRequest request = new BillOcrRequest();
//        request.setFile(null);
//        CommonResult<BillInformationResponse> result = medicalCasesController.billOcr(request);
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(Integer.valueOf(1001), result.getCode());
//        assertEquals("文件URL不能为空", result.getMsg());
//    }
//
//    @Test
//    public void testListOcrWithValidUrl() {
//        // 准备测试数据
//        String fileUrl = "https://example.com/list.pdf";
//        ListOcrRequest request = new ListOcrRequest();
//        request.setFile(fileUrl);
//
//        ListOcrResponse mockResponse = new ListOcrResponse();
//        mockResponse.setStatus("success");
//        mockResponse.setMessage("识别成功");
//
//        // 模拟AiUtils的返回
//        when(aiUtils.listOcr(anyString())).thenReturn(mockResponse);
//
//        // 执行测试
//        CommonResult<ListOcrResponse> result = medicalCasesController.listOcr(request);
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(Integer.valueOf(200), result.getCode());
//        assertNotNull(result.getData());
//        assertEquals("success", result.getData().getStatus());
//        assertEquals("识别成功", result.getData().getMessage());
//    }
//
//    @Test
//    public void testListOcrWithEmptyUrl() {
//        // 执行测试 - 空URL
//        ListOcrRequest request = new ListOcrRequest();
//        request.setFile("");
//        CommonResult<ListOcrResponse> result = medicalCasesController.listOcr(request);
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(Integer.valueOf(1001), result.getCode());
//        assertEquals("文件URL不能为空", result.getMsg());
//    }
//
//    @Test
//    public void testListOcrWithNullUrl() {
//        // 执行测试 - null URL
//        CommonResult<ListOcrResponse> result = medicalCasesController.listOcr(null);
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(Integer.valueOf(1001), result.getCode());
//        assertEquals("文件URL不能为空", result.getMsg());
//    }
//
//    @Test
//    public void testListOcrWithNullFileInRequest() {
//        // 执行测试 - request中file为null
//        ListOcrRequest request = new ListOcrRequest();
//        request.setFile(null);
//        CommonResult<ListOcrResponse> result = medicalCasesController.listOcr(request);
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(Integer.valueOf(1001), result.getCode());
//        assertEquals("文件URL不能为空", result.getMsg());
//    }
//
//    @Test
//    public void testBillOcrWithException() {
//        // 准备测试数据
//        String fileUrl = "https://example.com/bill.pdf";
//        BillOcrRequest request = new BillOcrRequest();
//        request.setFile(fileUrl);
//
//        // 模拟AiUtils抛出异常
//        when(aiUtils.billOcr(anyString())).thenThrow(new RuntimeException("网络连接失败"));
//
//        // 执行测试
//        CommonResult<BillInformationResponse> result = medicalCasesController.billOcr(request);
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(Integer.valueOf(1001), result.getCode());
//        assertTrue(result.getMsg().contains("账单OCR识别失败"));
//        assertTrue(result.getMsg().contains("网络连接失败"));
//    }
//
//    @Test
//    public void testListOcrWithException() {
//        // 准备测试数据
//        String fileUrl = "https://example.com/list.pdf";
//        ListOcrRequest request = new ListOcrRequest();
//        request.setFile(fileUrl);
//
//        // 模拟AiUtils抛出异常
//        when(aiUtils.listOcr(anyString())).thenThrow(new RuntimeException("网络连接失败"));
//
//        // 执行测试
//        CommonResult<ListOcrResponse> result = medicalCasesController.listOcr(request);
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(Integer.valueOf(1001), result.getCode());
//        assertTrue(result.getMsg().contains("清单OCR识别失败"));
//        assertTrue(result.getMsg().contains("网络连接失败"));
//    }
//}
