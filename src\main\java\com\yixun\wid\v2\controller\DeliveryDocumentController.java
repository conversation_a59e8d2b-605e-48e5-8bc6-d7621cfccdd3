package com.yixun.wid.v2.controller;

import com.yixun.bean.CommonResult;
import com.yixun.wid.entity.DeliveryFile;
import com.yixun.wid.entity.DeliveryFileItem;
import com.yixun.wid.entity.MailFileItem;
import com.yixun.wid.service.DeliveryFileService;
import com.yixun.wid.utils.DocGenerateUtil;
import com.yixun.wid.v2.bean.in.DeliveryDocumentDownloadIn;
import com.yixun.wid.v2.bean.in.DeliveryDocumentGenerateIn;
import com.yixun.wid.v2.vo.DeliveryDocumentGenerateOut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 送达文书控制器
 */
@Slf4j
@RequestMapping("/v2/delivery/document")
@RestController
public class DeliveryDocumentController {

    @Resource
    private DeliveryFileService deliveryFileService;

    /**
     * 文件存储路径
     */
    @Value("${filePath}")
    private String filePath;

    /**
     * 生成送达文书
     *
     * @param generateIn 生成请求参数
     * @return 生成结果
     */
    @PostMapping("/generate")
    public CommonResult<DeliveryDocumentGenerateOut> generateDeliveryDocument(@RequestBody DeliveryDocumentGenerateIn generateIn) {
        if (generateIn.getDeliveryFileId() == null) {
            return CommonResult.failResult(10001, "送达文件ID不能为空");
        }
        if (!StringUtils.hasText(generateIn.getDeliveryType())) {
            return CommonResult.failResult(10001, "送达类型不能为空");
        }

        try {
            // 获取送达文件信息
            DeliveryFile deliveryFile = deliveryFileService.getById(generateIn.getDeliveryFileId());
            if (deliveryFile == null) {
                return CommonResult.failResult(10002, "送达文件不存在");
            }

            // 创建目录
            String dirPath = filePath + "deliveryDocument/" + generateIn.getDeliveryFileId() + "/";
            File dir = new File(dirPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成送达回证文件列表
            List<File> deliveryReceiptFiles = new ArrayList<>();
            List<String> fileNames = new ArrayList<>();

            // 根据送达类型生成送达回证
            if ("邮寄".equals(generateIn.getDeliveryType())) {
                // 邮寄送达：生成在"送达材料"信息中，类型为邮寄送达的各个文书各生成一份送达回证
                if (!CollectionUtils.isEmpty(deliveryFile.getFileList())) {
                    for (Object item : deliveryFile.getFileList()) {
                        if (item instanceof DeliveryFileItem) {
                            DeliveryFileItem fileItem = (DeliveryFileItem) item;
                            if ("邮寄".equals(fileItem.getType())) {
                                // 生成送达回证
                                File receiptFile = generateDeliveryReceiptFile(deliveryFile, fileItem, "邮寄", dirPath);
                                if (receiptFile != null) {
                                    deliveryReceiptFiles.add(receiptFile);
                                    fileNames.add(receiptFile.getName());
                                }
                            }
                        }
                    }
                }
            } else if ("自领".equals(generateIn.getDeliveryType())) {
                // 材料自领：生成在"送达材料"信息中，类型为自领的各个文书，文书中的"受送达人"信息与材料领取人信息同步
                if (!CollectionUtils.isEmpty(deliveryFile.getFileList())) {
                    for (Object item : deliveryFile.getFileList()) {
                        if (item instanceof DeliveryFileItem) {
                            DeliveryFileItem fileItem = (DeliveryFileItem) item;
                            if ("自领".equals(fileItem.getType())) {
                                // 生成送达回证
                                File receiptFile = generateDeliveryReceiptFile(deliveryFile, fileItem, "自领", dirPath);
                                if (receiptFile != null) {
                                    deliveryReceiptFiles.add(receiptFile);
                                    fileNames.add(receiptFile.getName());
                                }
                            }
                        }
                    }
                }
            }

            if (deliveryReceiptFiles.isEmpty()) {
                return CommonResult.failResult(10003, "没有找到需要生成送达回证的文书");
            }

            // 打包成ZIP文件
            String zipFileName = "送达回证-" + getReceiverName(deliveryFile, generateIn.getDeliveryType()) + ".zip";
            String zipFilePath = dirPath + zipFileName;
            createZipFile(deliveryReceiptFiles, zipFilePath);

            // 返回结果
            DeliveryDocumentGenerateOut out = new DeliveryDocumentGenerateOut();
            out.setSuccess(true);
            out.setFilePath(zipFilePath);
            out.setFileName(zipFileName);

            return CommonResult.successData(out);
        } catch (Exception e) {
            log.error("生成送达文书失败", e);
            DeliveryDocumentGenerateOut out = new DeliveryDocumentGenerateOut();
            out.setSuccess(false);
            out.setErrorMessage("生成失败，请重试");
            return CommonResult.successData(out);
        }
    }

    /**
     * 下载送达文书
     *
     * @param downloadIn 下载请求参数
     * @param response   HTTP响应
     */
    @PostMapping("/download")
    public void downloadDeliveryDocument(@RequestBody DeliveryDocumentDownloadIn downloadIn, HttpServletResponse response) {
        if (downloadIn.getDeliveryFileId() == null) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return;
        }
        if (!StringUtils.hasText(downloadIn.getDeliveryType())) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return;
        }

        try {
            // 获取送达文件信息
            DeliveryFile deliveryFile = deliveryFileService.getById(downloadIn.getDeliveryFileId());
            if (deliveryFile == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 构建ZIP文件路径
            String dirPath = filePath + "deliveryDocument/" + downloadIn.getDeliveryFileId() + "/";
            String zipFileName = "送达回证-" + getReceiverName(deliveryFile, downloadIn.getDeliveryType()) + ".zip";
            String zipFilePath = dirPath + zipFileName;

            File zipFile = new File(zipFilePath);
            if (!zipFile.exists()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 设置响应头
            response.setContentType("application/zip");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(zipFileName, StandardCharsets.UTF_8.name()));

            // 输出文件
            try (FileInputStream fis = new FileInputStream(zipFile);
                 OutputStream os = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int len;
                while ((len = fis.read(buffer)) > 0) {
                    os.write(buffer, 0, len);
                }
                os.flush();
            }
        } catch (Exception e) {
            log.error("下载送达文书失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 生成送达回证文件
     *
     * @param deliveryFile 送达文件信息
     * @param fileItem     送达文件项
     * @param deliveryType 送达类型
     * @param dirPath      目录路径
     * @return 送达回证文件
     */
    private File generateDeliveryReceiptFile(DeliveryFile deliveryFile, DeliveryFileItem fileItem, String deliveryType, String dirPath) {
        try {
            // 准备模板数据
            Map<String, Object> dataMap = new HashMap<>();
            
            // 案由："受伤职工姓名工伤认定案"
            dataMap.put("caseCause", deliveryFile.getName() + "工伤认定案");
            
            // 送达文书名称、案件编号
            dataMap.put("documentName", fileItem.getFileType());
            dataMap.put("documentNumber", fileItem.getWritSn());
            
            // 受送达人
            String recipient = getRecipientName(deliveryFile, fileItem, deliveryType);
            dataMap.put("recipient", recipient);
            
            // 送达方式
            String deliveryMethod = getDeliveryMethod(deliveryFile, deliveryType);
            dataMap.put("deliveryMethod", deliveryMethod);
            
            // 生成文件名：送达回证（文书编号-送达类别-收件人/领取人姓名）
            String receiverName = getReceiverName(deliveryFile, deliveryType);
            String outputFileName = "送达回证（" + fileItem.getWritSn() + "-" + deliveryType + "-" + receiverName + "）.docx";
            
            // 生成Word文档
            DocGenerateUtil.generateWord(dataMap, "成都市人力资源和社会保障局-送达回证.docx", dirPath, outputFileName, null);
            
            return new File(dirPath + outputFileName);
        } catch (Exception e) {
            log.error("生成送达回证文件失败", e);
            return null;
        }
    }

    /**
     * 获取受送达人名称
     *
     * @param deliveryFile 送达文件信息
     * @param fileItem     送达文件项
     * @param deliveryType 送达类型
     * @return 受送达人名称
     */
    private String getRecipientName(DeliveryFile deliveryFile, DeliveryFileItem fileItem, String deliveryType) {
        if ("邮寄".equals(deliveryType)) {
            // 邮寄送达
            if ("用人单位".equals(fileItem.getRelationship())) {
                // "送达材料"信息中，送达对象为用人单位的："用人单位名称 收件人姓名"
                if (!CollectionUtils.isEmpty(deliveryFile.getMailList())) {
                    MailFileItem mailItem = deliveryFile.getMailList().get(0);
                    return deliveryFile.getOrganization() + " " + mailItem.getReceiver();
                }
            } else {
                // "送达材料"信息中，送达对象为职工或亲属的："收件人姓名"
                if (!CollectionUtils.isEmpty(deliveryFile.getMailList())) {
                    MailFileItem mailItem = deliveryFile.getMailList().get(0);
                    return mailItem.getReceiver();
                }
            }
        } else if ("自领".equals(deliveryType)) {
            // 材料自领
            if (!CollectionUtils.isEmpty(deliveryFile.getSelfList())) {
                Object selfItem = deliveryFile.getSelfList().get(0);
                // 这里假设selfList中的对象有getRecipientType和getRecipientName方法
                // 实际情况可能需要根据具体的数据结构进行调整
                try {
                    String recipientType = (String) selfItem.getClass().getMethod("getRecipientType").invoke(selfItem);
                    String recipientName = (String) selfItem.getClass().getMethod("getRecipientName").invoke(selfItem);
                    
                    if ("用人单位".equals(recipientType)) {
                        // 领取人类别为用人单位的："用人单位名称 收件人姓名"
                        return deliveryFile.getOrganization() + " " + recipientName;
                    } else {
                        // 领取人类别为职工或近亲属的："领取人"
                        return recipientName;
                    }
                } catch (Exception e) {
                    log.error("获取自领信息失败", e);
                }
            }
        }
        return "";
    }

    /**
     * 获取送达方式
     *
     * @param deliveryFile 送达文件信息
     * @param deliveryType 送达类型
     * @return 送达方式
     */
    private String getDeliveryMethod(DeliveryFile deliveryFile, String deliveryType) {
        if ("邮寄".equals(deliveryType)) {
            // 邮寄送达："邮寄送达 运单号"
            if (!CollectionUtils.isEmpty(deliveryFile.getMailList())) {
                MailFileItem mailItem = deliveryFile.getMailList().get(0);
                return "邮寄送达 " + mailItem.getMailSn();
            }
        } else if ("自领".equals(deliveryType)) {
            // 材料自领："直接送达"
            return "直接送达";
        }
        return "";
    }

    /**
     * 获取收件人/领取人姓名
     *
     * @param deliveryFile 送达文件信息
     * @param deliveryType 送达类型
     * @return 收件人/领取人姓名
     */
    private String getReceiverName(DeliveryFile deliveryFile, String deliveryType) {
        if ("邮寄".equals(deliveryType)) {
            // 邮寄送达
            if (!CollectionUtils.isEmpty(deliveryFile.getMailList())) {
                MailFileItem mailItem = deliveryFile.getMailList().get(0);
                return mailItem.getReceiver();
            }
        } else if ("自领".equals(deliveryType)) {
            // 材料自领
            if (!CollectionUtils.isEmpty(deliveryFile.getSelfList())) {
                Object selfItem = deliveryFile.getSelfList().get(0);
                try {
                    return (String) selfItem.getClass().getMethod("getRecipientName").invoke(selfItem);
                } catch (Exception e) {
                    log.error("获取自领人姓名失败", e);
                }
            }
        }
        return "";
    }

    /**
     * 创建ZIP文件
     *
     * @param files      要打包的文件列表
     * @param zipFilePath ZIP文件路径
     * @throws IOException IO异常
     */
    private void createZipFile(List<File> files, String zipFilePath) throws IOException {
        try (ZipOutputStream zos = new ZipOutputStream(new java.io.FileOutputStream(zipFilePath))) {
            for (File file : files) {
                try (FileInputStream fis = new FileInputStream(file)) {
                    ZipEntry zipEntry = new ZipEntry(file.getName());
                    zos.putNextEntry(zipEntry);
                    
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = fis.read(buffer)) > 0) {
                        zos.write(buffer, 0, len);
                    }
                    
                    zos.closeEntry();
                }
            }
        }
    }
}