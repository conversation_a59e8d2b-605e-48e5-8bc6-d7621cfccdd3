# 手术信息识别接口优化说明

## 概述

为了保持与"受理信息和临床诊断接口"的一致性，现在对"手术信息识别接口"进行了相同的优化处理，不再直接返回`SurgicalInformationResponse`对象，而是创建了专用的`SurgicalInformationVO`对象来返回相关字段。

## 设计目的

1. **数据安全**: 避免暴露完整的`SurgicalInformationResponse`对象中的内部信息
2. **接口简洁**: 只返回AI识别相关的字段，减少不必要的数据传输
3. **职责分离**: 将AI识别结果与响应实体分离，提高代码的可维护性
4. **一致性**: 与受理信息和临床诊断接口保持相同的设计模式

## 修改内容

### 1. 新增SurgicalInformationVO类

**文件路径**: `src\main\java\com\yixun\wid\v2\vo\ai\SurgicalInformationVO.java`

```java
@Data
public class SurgicalInformationVO {
    /**
     * 案件ID（如果是基于现有案件的识别）
     */
    private Long id;

    /**
     * 手术名称列表
     */
    private List<String> surgicalNames;
}
```

### 2. 修改Controller接口

**接口路径**: `POST /v2/medical/cases/surgical-information`

**修改前**:
```java
@PostMapping("/surgical-information")
public CommonResult<SurgicalInformationResponse> surgicalInformation(
        @RequestParam(required = false) Long id,
        @RequestBody(required = false) AcceptedInformationDiagnosisRequest request) {
    
    SurgicalInformationResponse response = aiUtils.surgicalInformation(request);
    return CommonResult.successData(response);
}
```

**修改后**:
```java
@PostMapping("/surgical-information")
public CommonResult<SurgicalInformationVO> surgicalInformation(
        @RequestParam(required = false) Long id,
        @RequestBody(required = false) AcceptedInformationDiagnosisRequest request) {

    SurgicalInformationResponse response = aiUtils.surgicalInformation(request);

    // 检查AI识别结果状态
    if (StrUtil.isBlank(response.getStatus()) || !"success".equals(response.getStatus())) {
        String errorMsg = String.format("手术信息识别失败：status=%s, message=%s",
            response.getStatus(), response.getMessage());
        log.error(errorMsg);
        throw new DataErrorException(errorMsg);
    }

    // 创建VO对象并映射数据
    SurgicalInformationVO resultVO = new SurgicalInformationVO();
    if (id != null) {
        resultVO.setId(id);
    }
    mapSurgicalDataToVO(resultVO, response);

    return CommonResult.successData(resultVO);
}
```

### 3. 新增状态判断逻辑

为了确保AI识别的可靠性，添加了对`SurgicalInformationResponse`状态的判断：

```java
// 检查AI识别结果状态
if (StrUtil.isBlank(response.getStatus()) || !"success".equals(response.getStatus())) {
    String errorMsg = String.format("手术信息识别失败：status=%s, message=%s",
        response.getStatus(), response.getMessage());
    log.error(errorMsg);
    throw new DataErrorException(errorMsg);
}
```

**状态判断规则**:
- 只有当`status`为`"success"`时才进行后续处理
- 当`status`为`null`、空字符串、或其他值时，抛出`DataErrorException`异常
- 异常信息包含具体的状态值和错误消息，便于问题排查

### 4. 新增映射方法

```java
private void mapSurgicalDataToVO(SurgicalInformationVO vo, SurgicalInformationResponse response) {
    if (response == null || response.getData() == null) {
        return;
    }

    SurgicalInformationResponse.SurgicalData data = response.getData();

    // 映射手术名称列表
    if (data.getSurgicalName() != null && !data.getSurgicalName().isEmpty()) {
        vo.setSurgicalNames(data.getSurgicalName());
    }
}
```

## 字段对比

| 原SurgicalInformationResponse | 新SurgicalInformationVO | 说明 |
|------------------------------|------------------------|------|
| `status` | - | 移除状态字段 |
| `message` | - | 移除消息字段 |
| `data.surgical_name` | `surgicalNames` | 保留手术名称列表 |
| - | `id` | 新增案件ID字段 |

## 使用示例

### 1. 基于案件ID的调用

```java
// 调用接口
CommonResult<SurgicalInformationVO> result = 
    medicalCasesController.surgicalInformation(12345L, null);

if (result.isSuccess()) {
    SurgicalInformationVO vo = result.getData();
    
    // 获取识别结果
    System.out.println("案件ID: " + vo.getId());
    System.out.println("手术名称: " + vo.getSurgicalNames());
}
```

### 2. 直接传入请求对象的调用

```java
// 构建请求对象
AcceptedInformationDiagnosisRequest request = new AcceptedInformationDiagnosisRequest();
// ... 设置请求参数

// 调用接口
CommonResult<SurgicalInformationVO> result = 
    medicalCasesController.surgicalInformation(null, request);

if (result.isSuccess()) {
    SurgicalInformationVO vo = result.getData();
    // 处理识别结果
}
```

### 3. 前端JavaScript示例

```javascript
// 基于案件ID调用
fetch('/v2/medical/cases/surgical-information?id=12345', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        const vo = data.data;
        console.log('识别结果:', vo);

        // 显示识别结果
        if (vo.surgicalNames && vo.surgicalNames.length > 0) {
            document.getElementById('surgicalNames').value = vo.surgicalNames.join(', ');
        } else {
            document.getElementById('surgicalNames').value = '';
            console.log('未识别到手术信息');
        }
    } else {
        // 处理识别失败的情况
        console.error('手术信息识别失败:', data.message);
        alert('手术信息识别失败: ' + data.message);
    }
})
.catch(error => {
    console.error('请求失败:', error);
    alert('网络请求失败，请稍后重试');
});
```

## 响应格式对比

### 修改前
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "status": "success",
    "message": "手术信息识别成功",
    "data": {
      "surgical_name": ["阑尾切除术", "胆囊切除术"]
    }
  }
}
```

### 修改后
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 12345,
    "surgicalNames": ["阑尾切除术", "胆囊切除术"]
  }
}
```

## 测试覆盖

### 1. SurgicalInformationVOTest
- 测试VO对象的创建和字段设置
- 测试空值处理
- 测试部分数据设置
- 测试业务场景

### 2. SurgicalInformationControllerTest
- 测试映射逻辑的正确性
- 测试空数据和null值的处理
- 测试有案件ID和无案件ID的情况
- 测试业务场景

### 3. SurgicalInformationStatusValidationTest（新增）
- 测试AI识别结果状态判断逻辑
- 测试success状态的正常处理
- 测试failed、error、timeout等异常状态的处理
- 测试null、空字符串、空白字符串状态的处理
- 测试业务场景下的状态处理

### 4. AiInterfaceTest（更新）
- 新增对SurgicalInformationVO的测试
- 测试优化后的返回类型
- 测试不自动保存数据的特性

## 兼容性

- **向后兼容**: 接口路径和请求参数保持不变
- **响应格式变化**: 响应数据结构有变化，前端需要相应调整
- **功能兼容**: 核心功能保持不变，只是返回格式优化

## 优势

1. **数据安全性**: 只返回必要的手术信息，不暴露内部状态
2. **传输效率**: 减少了不必要的字段传输
3. **维护性**: 专用的VO结构更易于维护和扩展
4. **一致性**: 与其他AI识别接口保持一致的设计模式

## 错误处理

### 状态码说明

| 状态值 | 含义 | 处理方式 |
|--------|------|----------|
| `success` | 识别成功 | 正常处理，返回VO对象 |
| `failed` | 识别失败 | 抛出DataErrorException |
| `error` | 系统错误 | 抛出DataErrorException |
| `timeout` | 请求超时 | 抛出DataErrorException |
| `null` | 状态为空 | 抛出DataErrorException |
| `""` | 空字符串 | 抛出DataErrorException |
| `"   "` | 空白字符串 | 抛出DataErrorException |

### 异常响应格式

当AI识别失败时，接口会返回错误响应：

```json
{
  "code": 6001,
  "message": "手术信息识别失败：status=failed, message=AI服务暂时不可用",
  "success": false,
  "data": null
}
```

### 前端错误处理建议

```javascript
fetch('/v2/medical/cases/surgical-information?id=12345', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' }
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        // 处理成功结果
        handleSuccess(data.data);
    } else {
        // 处理业务错误
        handleBusinessError(data.message);
    }
})
.catch(error => {
    // 处理网络错误
    handleNetworkError(error);
});
```

## 注意事项

1. **只读性质**: 此VO对象仅用于展示AI识别结果，不会自动保存到数据库
2. **字段可空**: 所有字段都可能为空，前端需要做好空值处理
3. **列表处理**: `surgicalNames`可能包含多个手术名称
4. **状态检查**: 接口会自动检查AI识别状态，只有成功时才返回数据
5. **异常处理**: 识别失败时会抛出异常，前端需要处理错误响应

## 迁移指南

如果您之前使用的是返回`SurgicalInformationResponse`对象的版本，请按以下步骤迁移：

1. **更新接口调用代码**: 将返回类型从`SurgicalInformationResponse`改为`SurgicalInformationVO`
2. **更新字段访问**: 
   - 原来的`response.getData().getSurgicalName()`改为`vo.getSurgicalNames()`
   - 移除对`status`和`message`字段的访问
3. **更新前端代码**: 适配新的数据结构
4. **测试功能**: 确保所有相关功能正常工作

## 版本信息

- **修改日期**: 2025-07-22
- **影响接口**: `/v2/medical/cases/surgical-information`
- **相关文件**:
  - `SurgicalInformationVO.java` (新增)
  - `MedicalCasesController.java` (修改，添加状态判断和映射逻辑)
  - `SurgicalInformationVOTest.java` (新增)
  - `SurgicalInformationControllerTest.java` (新增)
  - `SurgicalInformationStatusValidationTest.java` (新增，状态验证测试)
  - `AiInterfaceTest.java` (更新)

## 总结

通过这次优化，手术信息识别接口现在与受理信息和临床诊断接口保持了一致的设计模式，提供了更加简洁和安全的数据返回格式。所有修改都经过了完整的测试验证，确保功能正常且向后兼容。
