package com.yixun.wid.v2.vo.ai;

import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Collections;

/**
 * API结构测试类
 * 验证更新后的API数据结构是否正确
 */
public class ApiStructureTest {

    @Test
    public void testMaterialClassificationResponseStructure() {
        // 测试MaterialClassificationResponse的新结构
        MaterialClassificationResponse response = new MaterialClassificationResponse();
        response.setStatus("success");
        response.setMessage("测试成功");

        MaterialClassificationResponse.ClassificationData data = 
            new MaterialClassificationResponse.ClassificationData();
        
        // 测试医疗材料结构
        MaterialClassificationResponse.MedicalMaterials medical = 
            new MaterialClassificationResponse.MedicalMaterials();
        
        // 测试新的MedicalRecord结构
        MaterialClassificationResponse.MedicalRecord medicalRecord = 
            new MaterialClassificationResponse.MedicalRecord();
        medicalRecord.setMedicalRecordSubcategory(Collections.singletonList("病历子类.pdf"));
        medicalRecord.setDischargeCertificate(Collections.singletonList("出院证明.pdf"));
        medicalRecord.setDiagnosisCertificate(Collections.singletonList("诊断证明.pdf"));
        
        medical.setMedicalRecord(medicalRecord);
        medical.setSurgicalRecord(Collections.singletonList("手术记录.pdf"));
        medical.setElectronicInvoice(Collections.singletonList("电子发票.pdf"));
        
        data.setMedical(medical);
        response.setData(data);

        // 验证JSON序列化
        String json = JSON.toJSONString(response);
        System.out.println("MaterialClassificationResponse JSON: " + json);
        
        // 验证包含新字段
        assert json.contains("medical_record_subcategory");
        assert json.contains("discharge_certificate");
        assert json.contains("diagnosis_certificate");
        
        System.out.println("MaterialClassificationResponse结构测试通过");
    }

    @Test
    public void testAcceptedInformationDiagnosisRequestStructure() {
        // 测试AcceptedInformationDiagnosisRequest的新结构
        AcceptedInformationDiagnosisRequest request = new AcceptedInformationDiagnosisRequest();
        
        // 创建申请材料
        AcceptedInformationDiagnosisRequest.ApplicationMaterials application =
            new AcceptedInformationDiagnosisRequest.ApplicationMaterials();
        application.setIdCard(Collections.singletonList("身份证.jpg"));
        request.setApplication(application);

        // 创建就诊材料
        AcceptedInformationDiagnosisRequest.MedicalVisit visit =
            new AcceptedInformationDiagnosisRequest.MedicalVisit();
        visit.setVisitType("住院");
        visit.setVisitDate("2024-01-01");

        AcceptedInformationDiagnosisRequest.MedicalMaterials materials =
            new AcceptedInformationDiagnosisRequest.MedicalMaterials();
        
        // 测试新的MedicalRecord结构
        AcceptedInformationDiagnosisRequest.MedicalRecord medicalRecord =
            new AcceptedInformationDiagnosisRequest.MedicalRecord();
        medicalRecord.setMedicalRecordSubcategory(Collections.singletonList("病历子类.pdf"));
        medicalRecord.setMedicalConditionCertificate(Collections.singletonList("病情证明.pdf"));
        materials.setMedicalRecord(medicalRecord);
        
        materials.setElectronicInvoice(Collections.singletonList("电子发票.pdf"));
        visit.setMaterial(materials);
        request.setMedical(Collections.singletonList(visit));

        // 验证JSON序列化
        String json = JSON.toJSONString(request);
        System.out.println("AcceptedInformationDiagnosisRequest JSON: " + json);
        
        // 验证包含新字段
        assert json.contains("medical_record_subcategory");
        assert json.contains("medical_condition_certificate");
        
        System.out.println("AcceptedInformationDiagnosisRequest结构测试通过");
    }

    @Test
    public void testSurgicalInformationResponseStructure() {
        // 测试SurgicalInformationResponse的结构
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus("success");
        response.setMessage("手术信息识别成功");

        SurgicalInformationResponse.SurgicalData data =
            new SurgicalInformationResponse.SurgicalData();
        data.setSurgicalName(Collections.singletonList("阑尾切除术"));
        response.setData(data);

        // 验证JSON序列化
        String json = JSON.toJSONString(response);
        System.out.println("SurgicalInformationResponse JSON: " + json);

        // 验证包含预期字段
        assert json.contains("surgical_name");
        assert json.contains("阑尾切除术");

        System.out.println("SurgicalInformationResponse结构测试通过");
    }

    @Test
    public void testBillInformationResponseStructure() {
        // 测试BillInformationResponse的结构
        BillInformationResponse response = new BillInformationResponse();
        response.setStatus("success");
        response.setMessage("账单信息识别成功");

        BillInformationResponse.BillData data =
            new BillInformationResponse.BillData();

        BillInformationResponse.BillInformation billInfo =
            new BillInformationResponse.BillInformation();
        billInfo.setBillNumber("INV20240101001");
        billInfo.setHospital("测试医院");
        billInfo.setTreatmentType("住院");
        billInfo.setBillAmount(new BigDecimal("1500.00"));

        data.setBillInformation(Collections.singletonList(billInfo));
        response.setData(data);

        // 验证JSON序列化
        String json = JSON.toJSONString(response);
        System.out.println("BillInformationResponse JSON: " + json);

        // 验证包含预期字段
        assert json.contains("bill_Information");
        assert json.contains("bill_number");
        assert json.contains("treatment_type");
        assert json.contains("bill_amount");
        assert json.contains("INV20240101001");

        System.out.println("BillInformationResponse结构测试通过");
    }
}
