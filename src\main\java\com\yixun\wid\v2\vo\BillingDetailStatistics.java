package com.yixun.wid.v2.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 账单明细统计结果
 */
@Data
public class BillingDetailStatistics {
    
    /**
     * 甲类金额
     */
    @ApiModelProperty(value = "甲类金额")
    private BigDecimal levelAAmount;
    
    /**
     * 乙类金额
     */
    @ApiModelProperty(value = "乙类金额")
    private BigDecimal levelBAmount;
    
    /**
     * 丙类金额
     */
    @ApiModelProperty(value = "丙类金额")
    private BigDecimal levelCAmount;
    
    /**
     * 未匹配项目金额
     */
    @ApiModelProperty(value = "未匹配项目金额")
    private BigDecimal unmatchedAmount;
    
    /**
     * 可报销金额
     */
    @ApiModelProperty(value = "可报销金额")
    private BigDecimal reimbursableAmount;
    
    /**
     * 不可报销金额
     */
    @ApiModelProperty(value = "不可报销金额")
    private BigDecimal nonReimbursableAmount;
    
    /**
     * 限价扣除金额
     */
    @ApiModelProperty(value = "限价扣除金额")
    private BigDecimal priceLimitDeductionAmount;
    
    /**
     * 非工伤费用扣除金额
     */
    @ApiModelProperty(value = "非工伤费用扣除金额")
    private BigDecimal nonWorkInjuryDeductionAmount;
}