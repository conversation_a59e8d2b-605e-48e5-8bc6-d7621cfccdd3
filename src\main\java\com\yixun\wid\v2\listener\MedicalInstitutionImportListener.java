package com.yixun.wid.v2.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.idev.excel.EasyExcel;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.entity.MedicalInstitutions;
import com.yixun.wid.v2.utils.AiUtils;
import com.yixun.wid.v2.vo.MedicalInstitutionImportVO;
import com.yixun.wid.v2.vo.SimilarAiCheckOut;
import com.yixun.wid.v2.vo.SimilarAiCheckResult;
import com.yixun.wid.v2.vo.ai.QuerySimilarityResponse;
import com.yixun.wid.v2.vo.ai.SimilarityResult;
import com.yixun.wid.v2.vo.medical.BatchImportResult;
import com.yixun.wid.v2.vo.medical.FailItem;
import com.yixun.wid.v2.vo.medical.SimilarInstitutionItem;
import com.yixun.wid.v2.vo.medical.SimilarInstitutionResult;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.xmlgraphics.util.ClasspathResource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 医疗机构导入监听器
 */
@Slf4j
public class MedicalInstitutionImportListener extends AnalysisEventListener<MedicalInstitutionImportVO> {

	/**
	 * 批量处理阈值
	 */
	private static final int BATCH_COUNT = 100;

	/**
	 * 记录处理的数据
	 */
	@Getter
	private final List<MedicalInstitutionImportVO> list = new ArrayList<>();

	/**
	 * 失败的记录
	 */
	@Getter
	private final List<FailItem> failItems = new ArrayList<>();

	/**
	 * 失败行号列表
	 */
	@Getter
	private final List<Integer> failRowNums = new ArrayList<>();

	/**
	 * MongoDB操作模板
	 */
	private final MongoTemplate mongoTemplate;

	/**
	 * 导入模式
	 */
	private final String importMode;

	/**
	 * 相似机构校验模式："confirm"-先确认, "direct"-直接导入
	 */
	private final String similarCheckMode;

	/**
	 * AI工具类
	 */
	private final AiUtils aiUtils;

	/**
	 * 相似机构列表
	 */
	@Getter
	private final List<SimilarInstitutionItem> similarInstitutions = new ArrayList<>();

	/**
	 * 成功导入的数量
	 */
	@Getter
	private int successCount = 0;

	/**
	 * 当前处理的行号
	 */
	private int currentRowIndex = 0;

	/**
	 * 原始Excel文件
	 */
	@Getter
	@Setter
	private File originalFile;

	/**
	 * 处理后的结果文件
	 */
	@Getter
	private File resultFile;

	/**
	 * 所有数据（包括原始数据和处理后的数据）
	 */
	@Getter
	private final List<MedicalInstitutionImportVO> allData = new ArrayList<>();

	public static final ClassPathResource RESOURCE = new ClassPathResource("file/医院信息导入模板.xlsx");

	/**
	 * 构造函数
	 *
	 * @param mongoTemplate MongoDB操作模板
	 * @param importMode 导入模式（SKIP-名称相同跳过, OVERRIDE-名称相同覆盖）
	 * @param similarCheckMode 相似机构校验模式："confirm"-先确认, "direct"-直接导入
	 * @param aiUtils AI工具类
	 */
	public MedicalInstitutionImportListener(MongoTemplate mongoTemplate, String importMode, String similarCheckMode, AiUtils aiUtils) {
		this.mongoTemplate = mongoTemplate;
		this.importMode = importMode;
		this.similarCheckMode = similarCheckMode;
		this.aiUtils = aiUtils;
	}

	@Override
	public void invoke(MedicalInstitutionImportVO data, AnalysisContext context) {
		currentRowIndex = context.readRowHolder().getRowIndex() + 1;
		// 为每个数据记录其对应的行号
		data.setRowIndex(currentRowIndex);
		list.add(data);
		allData.add(data); // 保存所有数据，用于后续回填

		// 达到BATCH_COUNT，需要批量处理一次数据，防止数据过多导致内存溢出
		if (list.size() >= BATCH_COUNT) {
			processData();
			list.clear();
		}
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext context) {
		// 处理剩余数据
		processData();
		list.clear();

		// 如果是"confirm"模式，生成带有相似机构信息的Excel文件
		if ("confirm".equalsIgnoreCase(similarCheckMode) && !similarInstitutions.isEmpty() && originalFile != null) {
			try {
				resultFile = generateResultFile();
			} catch (Exception e) {
				log.error("生成相似机构结果文件失败", e);
			}
		}
	}

	/**
	 * 处理导入数据
	 */
	private void processData() {
		for (MedicalInstitutionImportVO importVO : list) {
			try {
				// 机构名称不能为空
				if (StrUtil.isBlank(importVO.getName())) {
					throw new IllegalArgumentException("机构名称不能为空");
				}

				// 在"confirm"模式下检查相似机构
				if ("confirm".equalsIgnoreCase(similarCheckMode)) {
					String name = importVO.getName().trim();
					
					// 获取所有医疗机构名称作为目标列表
					List<String> targetList = new ArrayList<>();
					// 查询MongoDB中的所有医疗机构
					Query query = new Query();
					mongoTemplate.find(query, MedicalInstitutions.class)
						.forEach(institution -> {
							if (StrUtil.isNotBlank(institution.getName())) {
								targetList.add(institution.getName());
							}
						});
					
					// 如果没有目标机构，则跳过相似度检查
					if (CollUtil.isEmpty(targetList)) {
						continue;
					}
					
					// 使用新方法查询相似机构
					List<SimilarityResult> similarResults = aiUtils.findSimilarItems(name, targetList, 5, 0.8);
					
					if (CollUtil.isNotEmpty(similarResults)) {
						// 创建相似机构项
						SimilarInstitutionItem item = new SimilarInstitutionItem();
						item.setRowNum(importVO.getRowIndex());
						item.setOriginalName(name);

						// 从结果中提取相似机构名称
						List<String> similarNames = similarResults.stream()
							.map(SimilarityResult::getName)
							.collect(Collectors.toList());

						item.setSimilarNames(similarNames);
						similarInstitutions.add(item);

						// 在"confirm"模式下添加相似机构信息到VO
						String similarNamesStr = String.join("、", similarNames);
						importVO.setErrorMsg(similarNamesStr);

						// 跳过此记录的后续处理，因为需要用户确认
						FailItem failItem = new FailItem();
						failItem.setRowNum(importVO.getRowIndex());
						failItem.setReason("发现相似机构，请确认：" + similarNamesStr);
						failItems.add(failItem);
						failRowNums.add(importVO.getRowIndex());
						continue;
					}
				}

				// 解析数据
				MedicalInstitutions institution = parseInstitution(importVO);

				// 检查日期有效性
				if (institution.getStartDate() != null && institution.getEndDate() != null
					&& institution.getStartDate().after(institution.getEndDate())) {
					throw new IllegalArgumentException("开始日期不能晚于结束日期");
				}

				// 检查机构名称是否重复
				Query query = new Query(Criteria.where("name").is(institution.getName()));
				MedicalInstitutions existingInstitution = mongoTemplate.findOne(query, MedicalInstitutions.class);

				if (existingInstitution != null) {
					// 根据导入模式处理
					if ("skip".equalsIgnoreCase(importMode)) {
						// 名称相同跳过
						String errorMsg = "机构名称已存在，已跳过";
						importVO.setErrorMsg(errorMsg);
						FailItem failItem = new FailItem();
						failItem.setRowNum(importVO.getRowIndex());
						failItem.setReason(errorMsg);
						failItems.add(failItem);
						failRowNums.add(importVO.getRowIndex());
						continue;
					} else if ("override".equalsIgnoreCase(importMode)) {
						// 名称相同覆盖，保留原有ID和创建时间
						institution.setId(existingInstitution.getId());
						institution.setCreateTime(existingInstitution.getCreateTime());

						// 保留原有的未在导入数据中设置的字段
						if (institution.getAliases() == null) {
							institution.setAliases(existingInstitution.getAliases());
						}
						if (institution.getType() == null) {
							institution.setType(existingInstitution.getType());
						}
						if (institution.getHospitalLevel() == null) {
							institution.setHospitalLevel(existingInstitution.getHospitalLevel());
						}
						if (institution.getRegion() == null) {
							institution.setRegion(existingInstitution.getRegion());
						}
						if (institution.getIsMainHospital() == null) {
							institution.setIsMainHospital(existingInstitution.getIsMainHospital());
						}
						if (institution.getPriceLimitLevel() == null) {
							institution.setPriceLimitLevel(existingInstitution.getPriceLimitLevel());
						}
					}
				} else {
					// 新增机构，生成ID
					institution.setId(SnGeneratorUtil.getId());
					institution.setCreateTime(new Date());
				}

				// 设置更新时间
				institution.setUpdateTime(new Date());

				// 保存机构信息
				mongoTemplate.save(institution);
				successCount++;

			} catch (Exception e) {
				// 记录失败信息
				String errorMsg = e.getMessage();
				importVO.setErrorMsg(errorMsg);
				FailItem failItem = new FailItem();
				failItem.setRowNum(importVO.getRowIndex());
				failItem.setReason(errorMsg);
				failItems.add(failItem);
				failRowNums.add(importVO.getRowIndex());
				log.error("导入第{}行数据失败: {}", importVO.getRowIndex(), errorMsg);
			}
		}
	}

	/**
	 * 生成导入结果Excel文件，包含错误信息
	 *
	 * @return 包含错误信息的Excel文件
	 * @throws IOException 如果文件处理出错
	 */
	public File generateResultFile() throws IOException {
		if (originalFile == null) {
			throw new IllegalStateException("原始文件不能为空");
		}

		// 处理错误信息
		for (MedicalInstitutionImportVO vo : allData) {
			// 使用VO中记录的行号，而不是基于索引计算
			Integer rowIndex = vo.getRowIndex();

			// 设置错误信息，包括相似机构信息和其他导入错误
			for (FailItem failItem : failItems) {
				if (Objects.equals(failItem.getRowNum(), rowIndex)) {
					vo.setErrorMsg(failItem.getReason());
					break;
				}
			}
		}

		// 创建新的Excel文件
		File resultFile = FileUtil.createTempFile("import_result_", ".xlsx", true);

		try {
			// 获取模板文件输入流并创建临时文件
			File templateFile = null;
			try {
				// 从classpath资源创建临时模板文件
				templateFile = FileUtil.createTempFile("template_", ".xlsx", true);
				FileUtil.writeFromStream(RESOURCE.getInputStream(), templateFile);

				// 使用EasyExcel的withTemplate功能写入数据
				try (cn.idev.excel.ExcelWriter excelWriter = EasyExcel.write(FileUtil.getOutputStream(resultFile), MedicalInstitutionImportVO.class)
						.withTemplate(templateFile)
						.needHead(false)
						.build()) {
					cn.idev.excel.write.metadata.WriteSheet writeSheet = EasyExcel.writerSheet().build();
					excelWriter.write(allData, writeSheet);
				}
			} finally {
				// 清理临时模板文件
				if (templateFile != null && templateFile.exists()) {
					FileUtil.del(templateFile);
				}
			}
		} catch (Exception e) {
			log.error("使用模板生成结果文件失败：", e);
		}

		return resultFile;
	}

	/**
     * 从Excel行数据解析医疗机构信息
     *
     * @param importVO Excel导入数据
     * @return 医疗机构信息
     */
    private MedicalInstitutions parseInstitution(MedicalInstitutionImportVO importVO) {
        MedicalInstitutions institution = new MedicalInstitutions();
        List<String> errorMessages = new ArrayList<>();

        // 解析机构名称
        if (StrUtil.isBlank(importVO.getName())) {
            errorMessages.add("机构名称不能为空");
        } else {
            institution.setName(importVO.getName().trim());
        }

        // 解析机构别称 - 使用分号分隔
        if (StrUtil.isNotBlank(importVO.getAliases())) {
            List<String> aliasList = Arrays.stream(importVO.getAliases().split(";"))
                    .map(String::trim)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
            institution.setAliases(aliasList);
        }

        // 解析是否协议机构
        if (StrUtil.isNotBlank(importVO.getIsAgreementHospital())) {
            institution.setIsAgreementHospital("是".equals(importVO.getIsAgreementHospital())
                    || "true".equalsIgnoreCase(importVO.getIsAgreementHospital()));
        } else {
            errorMessages.add("是否协议机构不能为空");
        }

        // 解析机构等级
	    List<String> levelList = new ArrayList<>();
	    if (StrUtil.isNotBlank(importVO.getHospitalLevel())) {
		    levelList.add(importVO.getHospitalLevel());
			if (StrUtil.isNotBlank(importVO.getLevel2())) {
				levelList.add(importVO.getLevel2());
			}
	    }
		institution.setHospitalLevel(levelList);

        // 解析机构类型 - 使用分号分隔
        if (StrUtil.isNotBlank(importVO.getType())) {
            List<String> typeList = Arrays.stream(importVO.getType().split(";"))
                    .map(String::trim)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
            institution.setType(typeList);
        }

        // 校验：当是协议机构时，机构类型不能为空
        if (institution.getIsAgreementHospital() != null && institution.getIsAgreementHospital()) {
            if (StrUtil.isBlank(importVO.getType())) {
                errorMessages.add("协议机构的机构类型不能为空");
            }
        }

        // 解析开始日期 - 支持多种常见日期格式
        if (StrUtil.isNotBlank(importVO.getStartDate())) {
            try {
                // 使用通用日期格式解析
                Date startDate = DateUtil.parse(importVO.getStartDate().trim());
                institution.setStartDate(startDate);
            } catch (Exception e) {
                errorMessages.add("开始日期格式不正确，请确保日期格式正确");
            }
        }

        // 解析结束日期 - 支持多种常见日期格式
        if (StrUtil.isNotBlank(importVO.getEndDate())) {
            try {
                // 使用通用日期格式解析
                Date endDate = DateUtil.parse(importVO.getEndDate().trim());
                institution.setEndDate(endDate);
            } catch (Exception e) {
                errorMessages.add("结束日期格式不正确，请确保日期格式正确");
            }
        }

        // 检查日期有效性 - 开始日期不能晚于结束日期
        if (institution.getStartDate() != null && institution.getEndDate() != null
                && institution.getStartDate().after(institution.getEndDate())) {
            errorMessages.add("开始日期不能晚于结束日期");
        }

        // 解析省市区 - 按省/市/区格式填充
        if (StrUtil.isNotBlank(importVO.getRegion())) {
            List<String> regionList = Arrays.stream(importVO.getRegion().split("/"))
                    .map(String::trim)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
            institution.setRegion(regionList);
        }

        // 解析详细地址
        if (StrUtil.isNotBlank(importVO.getAddress())) {
            institution.setAddress(importVO.getAddress().trim());
        }

        // 解析经纬度 - 格式：经度,纬度
        if (StrUtil.isNotBlank(importVO.getLongitudeLatitude())) {
            try {
                String[] coordinates = importVO.getLongitudeLatitude().split(",");
                if (coordinates.length >= 2) {
                    try {
                        institution.setLongitude(Double.parseDouble(coordinates[0].trim()));
                        institution.setLatitude(Double.parseDouble(coordinates[1].trim()));
                    } catch (NumberFormatException e) {
                        errorMessages.add("经纬度格式不正确，应为：经度,纬度");
                    }
                } else {
                    errorMessages.add("经纬度格式不正确，应为：经度,纬度");
                }
            } catch (Exception e) {
                errorMessages.add("经纬度解析异常: " + e.getMessage());
            }
        }

        // 解析限价医院等级
        if (StrUtil.isNotBlank(importVO.getPriceLimitLevel())) {
            institution.setPriceLimitLevel(importVO.getPriceLimitLevel().trim());
        }

        // 如果有错误信息，抛出异常
        if (!errorMessages.isEmpty()) {
            throw new IllegalArgumentException(String.join("; ", errorMessages));
        }

        return institution;
    }
}
