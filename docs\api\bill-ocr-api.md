# 账单OCR接口文档

## 概述

本文档描述了新增的账单OCR相关接口，包括账单OCR识别和清单OCR识别功能。

## 接口列表

### 1. 账单OCR接口

#### 接口信息
- **URL**: `/v2/medical/cases/bill-ocr`
- **方法**: `POST`
- **描述**: 直接对单个账单文件进行OCR识别，无需依赖材料分类结果

#### 请求参数
请求体为JSON格式：
```json
{
  "file": "https://example.com/bill.pdf"
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| file | String | 是 | 账单文件的URL地址 |

#### 请求示例
```http
POST /v2/medical/cases/bill-ocr
Content-Type: application/json

{
  "file": "https://example.com/bill.pdf"
}
```

#### 响应参数
返回 `CommonResult<BillInformationResponse>` 格式的数据

**BillInformationResponse 结构**:
```json
{
  "status": "success",
  "message": "识别成功",
  "data": {
    "bill_Information": [
      {
        "bill_number": "INV20240101001",
        "hospital": "测试医院",
        "treatment_type": "住院",
        "bill_amount": 1500.00,
        "medical_co-ordination_fund": 800,
        "admission_time": "2024-01-01",
        "discharge_time": "2024-01-03",
        "stay_days": 2,
        "clinic_time": null,
        "expense_details": [
          {
            "expense_item": "治疗费",
            "amount": 500.00,
            "expense_list": [
              {
                "name": "项目1",
                "code": "001",
                "quantity": 1,
                "unit_price": 500.00,
                "amount": 500.00
              }
            ]
          }
        ]
      }
    ]
  }
}
```

#### 错误响应
```json
{
  "success": false,
  "code": 1001,
  "message": "文件URL不能为空"
}
```

### 2. 清单OCR接口

#### 接口信息
- **URL**: `/v2/medical/cases/list-ocr`
- **方法**: `POST`
- **描述**: 直接对单个清单文件进行OCR识别

#### 请求参数
请求体为JSON格式：
```json
{
  "file": "https://example.com/list.pdf"
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| file | String | 是 | 清单文件的URL地址 |

#### 请求示例
```http
POST /v2/medical/cases/list-ocr
Content-Type: application/json

{
  "file": "https://example.com/list.pdf"
}
```

#### 响应参数
返回 `CommonResult<ListOcrResponse>` 格式的数据

**ListOcrResponse 结构**:
```json
{
  "status": "success",
  "message": "识别成功",
  "data": {
    "expense_list": [
      {
        "name": "药品费用",
        "expense_item": "药品费",
        "code": "001",
        "quantity": 2,
        "unit_price": 50.00,
        "amount": 100.00
      },
      {
        "name": "检查费用",
        "expense_item": "检查治疗费",
        "code": "002",
        "quantity": 1,
        "unit_price": 200.00,
        "amount": 200.00
      }
    ]
  }
}
```

#### 错误响应
```json
{
  "success": false,
  "code": 1001,
  "message": "文件URL不能为空"
}
```

## 使用说明

### 1. 账单OCR vs 账单信息识别的区别

- **账单OCR** (`/bill-ocr`): 直接对单个账单文件进行OCR识别，适用于快速识别单个账单文件
- **账单信息识别** (`/bill-information`): 基于材料分类结果进行批量账单信息识别，适用于已完成材料分类的案件

### 2. 支持的文件格式

- PDF文件
- 图片文件（JPG、PNG等）

### 3. 注意事项

1. 文件URL必须是可公开访问的有效链接
2. 文件大小建议不超过10MB
3. 确保文件内容清晰，以提高识别准确率
4. 接口调用可能需要一定时间，建议设置合理的超时时间

### 4. 错误处理

接口会返回详细的错误信息，常见错误包括：
- 文件URL为空或无效
- 文件无法访问
- OCR识别失败
- 网络连接异常

## 集成示例

### JavaScript 示例

```javascript
// 账单OCR
async function billOcr(fileUrl) {
  try {
    const response = await fetch('/v2/medical/cases/bill-ocr', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        file: fileUrl
      })
    });

    const result = await response.json();

    if (result.code === 200) {
      console.log('账单OCR识别成功:', result.data);
      return result.data;
    } else {
      console.error('账单OCR识别失败:', result.msg);
      throw new Error(result.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
    throw error;
  }
}

// 清单OCR
async function listOcr(fileUrl) {
  try {
    const response = await fetch('/v2/medical/cases/list-ocr', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        file: fileUrl
      })
    });

    const result = await response.json();

    if (result.code === 200) {
      console.log('清单OCR识别成功:', result.data);
      return result.data;
    } else {
      console.error('清单OCR识别失败:', result.msg);
      throw new Error(result.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
    throw error;
  }
}
```

### Java 示例

```java
// 使用RestTemplate调用账单OCR接口
@Service
public class BillOcrService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public BillInformationResponse billOcr(String fileUrl) {
        String url = "/v2/medical/cases/bill-ocr";

        // 创建请求体
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("file", fileUrl);

        CommonResult<BillInformationResponse> result = restTemplate.postForObject(
            url,
            requestBody,
            new ParameterizedTypeReference<CommonResult<BillInformationResponse>>() {}
        );

        if (result != null && result.getCode() == 200) {
            return result.getData();
        } else {
            throw new RuntimeException("账单OCR识别失败: " + (result != null ? result.getMsg() : "未知错误"));
        }
    }

    public ListOcrResponse listOcr(String fileUrl) {
        String url = "/v2/medical/cases/list-ocr";

        // 创建请求体
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("file", fileUrl);

        CommonResult<ListOcrResponse> result = restTemplate.postForObject(
            url,
            requestBody,
            new ParameterizedTypeReference<CommonResult<ListOcrResponse>>() {}
        );

        if (result != null && result.getCode() == 200) {
            return result.getData();
        } else {
            throw new RuntimeException("清单OCR识别失败: " + (result != null ? result.getMsg() : "未知错误"));
        }
    }
}
```
