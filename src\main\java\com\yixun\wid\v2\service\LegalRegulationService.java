package com.yixun.wid.v2.service;

import com.yixun.bean.CommonPage;
import com.yixun.wid.v2.entity.LegalRegulation;
import com.yixun.wid.v2.vo.SortVO;

import java.util.List;

/**
 * 法规依据服务接口
 */
public interface LegalRegulationService {

    /**
     * 新增法规依据
     *
     * @param legalRegulation 法规依据
     * @return 新增后的法规依据
     */
    LegalRegulation save(LegalRegulation legalRegulation);

    /**
     * 查询法规依据列表
     *
     * @param status     状态
     * @param search     搜索关键词（法规名称）
     * @param commonPage 分页参数
     * @return 法规依据列表
     */
    List<LegalRegulation> list(Integer status, String search, CommonPage commonPage);

    /**
     * 查询法规依据列表（不分页）
     *
     * @param status  状态
     * @param search  搜索关键词
     * @return 法规依据列表
     */
    List<LegalRegulation> listAll(Integer status, String search);

    /**
     * 根据id查询法规依据
     *
     * @param id 主键
     * @return 法规依据
     */
    LegalRegulation getById(Long id);

    /**
     * 更新法规依据
     *
     * @param legalRegulation 法规依据
     * @return 更新后的法规依据
     */
    LegalRegulation update(LegalRegulation legalRegulation);

    /**
     * 批量删除法规依据
     *
     * @param ids 法规依据ID列表
     */
    void batchDelete(List<Long> ids);

    /**
     * 更新法规依据列表排序
     *
     * @param sort 列表排序
     */
    void updateSort(List<SortVO> sort);

    /**
     * 智能搜索法规依据
     *
     * @param regulationName 法规名称（可选）
     * @param content        条款内容（可选）
     * @return 法规依据列表
     */
    List<LegalRegulation> smartSearch(String regulationName, String content);
}