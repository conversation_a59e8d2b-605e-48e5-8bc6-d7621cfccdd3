package com.yixun.wid.v2.validation;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 身份证号码校验器
 * 使用hutool的IdcardUtil进行校验
 */
public class IdCardValidator implements ConstraintValidator<IdCard, String> {

    @Override
    public void initialize(IdCard constraintAnnotation) {
        // 初始化方法，可以获取注解参数
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 如果值为空或空字符串，则认为校验通过（允许为空）
        if (StrUtil.isBlank(value)) {
            return true;
        }
        
        // 使用hutool工具校验身份证格式
        return IdcardUtil.isValidCard(value.trim());
    }
}
