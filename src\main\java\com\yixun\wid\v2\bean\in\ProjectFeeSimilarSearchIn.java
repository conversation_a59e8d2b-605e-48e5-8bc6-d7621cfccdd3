package com.yixun.wid.v2.bean.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 项目费用相似搜索输入参数
 */
@Data
public class ProjectFeeSimilarSearchIn {
    
    /**
     * 医院ID（可选参数，与医院名称、医院别名三选一）
     */
    private Long hospitalId;
    
    /**
     * 医院名称（可选参数，与医院ID、医院别名三选一）
     */
    private String hospitalName;
    
    /**
     * 医院别名（可选参数，与医院ID、医院名称三选一）
     */
    private String hospitalAlias;
    
    /**
     * 项目名称（必填参数）
     */
    @NotBlank(message = "项目名称不能为空")
    private String projectName;
    
    /**
     * 返回结果数量（可选，默认为1）
     */
    private Integer topK = 1;
    
    /**
     * 相似度阈值（可选，默认为0.0）
     */
    private Double similarityThreshold = 0.0;
}
