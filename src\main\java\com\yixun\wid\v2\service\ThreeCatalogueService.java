package com.yixun.wid.v2.service;

import com.yixun.wid.v2.bean.in.ThreeCatalogueSimilarSearchIn;
import com.yixun.wid.v2.entity.ThreeCatalogue;

/**
 * 三目录服务接口
 */
public interface ThreeCatalogueService {
    
    /**
     * 三目录相似搜索
     * 基于AiUtils中的相似查找功能，实现三目录的智能匹配
     *
     * @param searchIn 相似搜索参数
     * @return 相似度最高的一条三目录数据的完整详情，未找到返回null
     */
    ThreeCatalogue similarSearch(ThreeCatalogueSimilarSearchIn searchIn);
}
