package com.yixun.wid.service.impl;

import cn.hutool.core.util.StrUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.DeliveryFileGetIn;
import com.yixun.wid.entity.*;
import com.yixun.wid.entity.em.DeclarationStatus;
import com.yixun.wid.service.DeliveryFileService;
import com.yixun.wid.service.MessageService;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class DeliveryFileServiceImpl implements DeliveryFileService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private MessageService messageService;

    @Override
    public void save(DeliveryFile deliveryFile) {
        mongoTemplate.save(deliveryFile);
    }

    @Override
    public DeliveryFile getById(Long deliveryFileId) {
        return mongoTemplate.findById(deliveryFileId, DeliveryFile.class);
    }

    @Override
    public void update(DeliveryFile deliveryFile) {
        deliveryFile.setUpdateTime(new Date());
        mongoTemplate.save(deliveryFile);
    }

    @Override
    public List<DeliveryFile> getList(DeliveryFileGetIn getIn, CommonPage commonPage) {
        Query query = new Query();
        query.addCriteria(Criteria.where("isConfirmed").ne(true));

        if (getIn.getName() != null) {
            query.addCriteria(Criteria.where("name").regex(getIn.getName()));
        }
        if (getIn.getStep() != null) {
            query.addCriteria(Criteria.where("step").is(getIn.getStep()));
        }
        if (getIn.getType() != null) {
            query.addCriteria(Criteria.where("type").regex(getIn.getType()));
        }
        if (getIn.getOrganization() != null) {
            query.addCriteria(Criteria.where("organization").regex(getIn.getOrganization()));
        }
        if (getIn.getStartDate() != null && getIn.getEndDate() != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(getIn.getEndDate());
            c.add(Calendar.SECOND, 86399); //结束时间加到当天的23:59:59
            query.addCriteria(Criteria.where("accidentTime").gte(getIn.getStartDate()).lte(c.getTime()));
        }

        query.with(Sort.by(Sort.Order.desc("createTime")));

        MongoUtil.setPageInfo(mongoTemplate, DeliveryFile.class, query, commonPage);
        return mongoTemplate.find(query, DeliveryFile.class);
    }

    @Override
    public DeliveryFile getByDeclaration(Long declarationId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("declarationId").is(declarationId));

        return mongoTemplate.findOne(query, DeliveryFile.class);
    }

    @Override
    public void handleDeliveryFile(Declaration declaration, String step, String fileType, String writSn) {
        DeliveryFile deliveryFile = getByDeclaration(declaration.getId());
        if (deliveryFile == null) {
            deliveryFile = new DeliveryFile();
            deliveryFile.setId(SnGeneratorUtil.getId());
            deliveryFile.setCreateTime(new Date());
            deliveryFile.setDeclarationId(declaration.getId());
            deliveryFile.setAccidentTime(declaration.getAccidentTime());
            deliveryFile.setUserId(declaration.getUserId());
            deliveryFile.setName(declaration.getName());
            deliveryFile.setStep(step);
            deliveryFile.setType(declaration.getInformReceiveWay());
            deliveryFile.setOrganization(declaration.getOrganization());
            deliveryFile.setCaseSn(declaration.getCaseSn());
            deliveryFile.setMailList(new ArrayList());
            deliveryFile.setSelfList(new ArrayList());

            DeliveryFileItem deliveryFileItem = new DeliveryFileItem();
            deliveryFileItem.setType(declaration.getInformReceiveWay());
            deliveryFileItem.setRelationship("职工");
            deliveryFileItem.setPages("1");
            deliveryFileItem.setFileType(fileType);
            deliveryFileItem.setWritSn(writSn);
            List<DeliveryFileItem> fileList = new ArrayList<>();
            fileList.add(deliveryFileItem);
            deliveryFile.setFileList(fileList);

            if (StrUtil.isNotBlank(declaration.getInformReceiveWay()) && "邮寄".equals(declaration.getInformReceiveWay())) {
                if (declaration.getInformReceiveAddr() != null) {
                    MailFileItem mailFileItem = new MailFileItem();
                    mailFileItem.setAddress(declaration.getInformReceiveAddr().getAddress());
                    mailFileItem.setPhone(declaration.getInformReceiveAddr().getPhone());
                    mailFileItem.setReceiver(declaration.getInformReceiveAddr().getName());

                    List<MailFileItem> mailList = new ArrayList<>();
                    mailList.add(mailFileItem);
                    deliveryFile.setMailList(mailList);
                }
            }

            save(deliveryFile);
        } else {
            DeliveryFileItem deliveryFileItem = new DeliveryFileItem();
            deliveryFileItem.setType(declaration.getInformReceiveWay());
            deliveryFileItem.setRelationship("职工");
            deliveryFileItem.setPages("1");
            deliveryFileItem.setFileType(fileType);
            deliveryFileItem.setWritSn(writSn);
            List fileList = deliveryFile.getFileList();
            fileList.add(deliveryFileItem);
            deliveryFile.setFileList(fileList);

            update(deliveryFile);
        }
    }

    @XxlJob("sendDoneMsHandler")
    @Override
    public void sendDoneMs() {
        Query query = new Query();
        query.addCriteria(Criteria.where("informReceiveSignature").isNull());
        query.addCriteria(Criteria.where("status").is(DeclarationStatus.Done));
        List<Declaration> declarations = mongoTemplate.find(query, Declaration.class);
        if (!CollectionUtils.isEmpty(declarations)) {
            List<Long> idList = declarations.stream()
                    .map(Declaration::getId)
                    .collect(Collectors.toList());
            // PC录入运单号2个自然日后未电子签名的
            Date twoDaysAgo = new Date(System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000);
            query = new Query();
            query.addCriteria(Criteria.where("confirmedTime").lte(twoDaysAgo));
            query.addCriteria(Criteria.where("type").is("邮寄"));
            query.addCriteria(Criteria.where("declarationId").in(idList));
            List<DeliveryFile> deliveryFiles = mongoTemplate.find(query, DeliveryFile.class);
            if (!CollectionUtils.isEmpty(deliveryFiles)) {
                List<Long> declarationIds = deliveryFiles.stream()
                        .map(DeliveryFile::getDeclarationId)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(declarationIds)) {
                    List<Declaration> collect = declarations.stream()
                            .filter(declaration -> declarationIds.contains(declaration.getId()))
                            .collect(Collectors.toList());
                    String message = "工伤认定结论已发出，请及时签收";
                    collect.forEach(e -> {
                        messageService.sendTrueWxMessage(e.getSubmitUserId(), "认定申请", "PC录入运单号2个自然日后未电子签名的",
                                message);
                    });
                }
            }
            //现场递送：1个工作日后未电子签名的
            Date oneDaysAgo = new Date(System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000);
            query = new Query();
            query.addCriteria(Criteria.where("confirmedTime").lte(oneDaysAgo));
            query.addCriteria(Criteria.where("type").is("自领"));
            query.addCriteria(Criteria.where("declarationId").in(idList));
            List<DeliveryFile> oneDeliveryFiles = mongoTemplate.find(query, DeliveryFile.class);
            if (!CollectionUtils.isEmpty(oneDeliveryFiles)) {
                List<Long> declarationIds = oneDeliveryFiles.stream()
                        .map(DeliveryFile::getDeclarationId)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(declarationIds)) {
                    List<Declaration> collect = declarations.stream()
                            .filter(declaration -> declarationIds.contains(declaration.getId()))
                            .collect(Collectors.toList());
                    String message = "工伤认定结论已发出，请及时签收";
                    collect.forEach(e -> {
                        messageService.sendTrueWxMessage(e.getSubmitUserId(), "认定申请", "现场递送：1个工作日后未电子签名的",
                                message);
                    });
                }
            }
        }

    }
}
