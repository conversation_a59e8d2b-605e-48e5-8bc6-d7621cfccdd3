package com.yixun.wid.v2.entity;

import org.springframework.data.annotation.Transient;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 理算信息
 */
@Data
public class ClaimsData {
    /**
     * 责任项目（回显，根据选择的责任条展示工伤保险责任/工伤医疗待遇类型）
     */
    private String responsibilityItem;

    /**
     * 历史赔付次数（回显，该受伤害职工该责任条历史赔付数量）
     */
    private Integer historyPayCount;

    /**
     * 历史给付天数（回显，仅住院费用责任条需显示，该职工历史赔付天数合计）
     */
    private Integer historyPayDays;

    /**
     * 历史赔付金额（分，数据库存储）
     */
    private Integer historyPayAmountInCent;
    /**
     * 历史赔付金额（元，接口交互，回显该职工历史赔付金额合计）
     */
    @Transient
    private BigDecimal historyPayAmount;

    /**
     * 住院伙食补助（自动选择住院责任条为"是"，否则为"否"，可修改）
     */
    private Boolean isHospitalFoodAllowance;

    /**
     * 津贴标准 （补助标准）（分，数据库存储，门诊责任条时禁用）
     */
    private Integer foodAllowanceStandardInCent;
    /**
     * 津贴标准 （补助标准）（元，接口交互，输入数字，门诊责任条时禁用）
     */
    @Transient
    private BigDecimal foodAllowanceStandard;

    /**
     * 共计给付天数（自动填充所有住院账单天数合计，不可修改，门诊责任条时禁用）
     */
    private Integer totalPayDays;

    /**
     * 住院伙食补助金额（分，数据库存储，自动计算=补助标准*给付天数，门诊禁用）
     */
    private Integer foodAllowanceAmountInCent;
    /**
     * 住院伙食补助金额（元，接口交互，自动计算=补助标准*给付天数，门诊禁用）
     */
    @Transient
    private BigDecimal foodAllowanceAmount;

    /**
     * 发票总金额（分，数据库存储，自动计算住院/门诊发票总金额合计）
     */
    private Integer invoiceTotalAmountInCent;
    /**
     * 发票总金额（元，接口交互，自动计算住院/门诊发票总金额合计）
     */
    @Transient
    private BigDecimal invoiceTotalAmount;

    /**
     * 发票张数（自动填充，住院/门诊发票账单数量）
     */
    private Integer invoiceCount;

    /**
     * 不可报销金额（分，数据库存储，自动计算账单审核扣减金额+非工伤扣减金额）
     */
    private Integer nonReimbursableAmountInCent;
    /**
     * 不可报销金额（元，接口交互，自动计算账单审核扣减金额+非工伤扣减金额）
     */
    @Transient
    private BigDecimal nonReimbursableAmount;

    /**
     * 可报销金额（分，数据库存储，自动计算所有账单合理费用）
     */
    private Integer reimbursableAmountInCent;
    /**
     * 可报销金额（元，接口交互，自动计算所有账单合理费用）
     */
    @Transient
    private BigDecimal reimbursableAmount;

    /**
     * 应付总金额（分，数据库存储，自动计算=可报销金额+住院伙食补助金额）
     */
    private Integer totalPayableAmountInCent;
    /**
     * 应付总金额（元，接口交互，自动计算=可报销金额+住院伙食补助金额）
     */
    @Transient
    private BigDecimal totalPayableAmount;

    /**
     * 核销明细-门诊
     * 将所有门诊发票按费用项目分项汇总计算
     */
    private OutpatientClearing outpatientClearing;

    /**
     * 核销明细-住院
     * 按每次住院（每个住院账单）展示基本核算信息
     */
    private HospitalClearing hospitalClearing;

    // 元分转换方法
    public BigDecimal getHistoryPayAmount() {
        if (historyPayAmountInCent == null) {
            return null;
        }
        return new BigDecimal(historyPayAmountInCent).divide(new BigDecimal(100));
    }

    public void setHistoryPayAmount(BigDecimal amount) {
        if (amount == null) {
            this.historyPayAmountInCent = null;
        } else {
            this.historyPayAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }

    public BigDecimal getFoodAllowanceStandard() {
        if (foodAllowanceStandardInCent == null) {
            return null;
        }
        return new BigDecimal(foodAllowanceStandardInCent).divide(new BigDecimal(100));
    }

    public void setFoodAllowanceStandard(BigDecimal amount) {
        if (amount == null) {
            this.foodAllowanceStandardInCent = null;
        } else {
            this.foodAllowanceStandardInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }

    public BigDecimal getFoodAllowanceAmount() {
        if (foodAllowanceAmountInCent == null) {
            return null;
        }
        return new BigDecimal(foodAllowanceAmountInCent).divide(new BigDecimal(100));
    }

    public void setFoodAllowanceAmount(BigDecimal amount) {
        if (amount == null) {
            this.foodAllowanceAmountInCent = null;
        } else {
            this.foodAllowanceAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }

    public BigDecimal getInvoiceTotalAmount() {
        if (invoiceTotalAmountInCent == null) {
            return null;
        }
        return new BigDecimal(invoiceTotalAmountInCent).divide(new BigDecimal(100));
    }

    public void setInvoiceTotalAmount(BigDecimal amount) {
        if (amount == null) {
            this.invoiceTotalAmountInCent = null;
        } else {
            this.invoiceTotalAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }

    public BigDecimal getNonReimbursableAmount() {
        if (nonReimbursableAmountInCent == null) {
            return null;
        }
        return new BigDecimal(nonReimbursableAmountInCent).divide(new BigDecimal(100));
    }

    public void setNonReimbursableAmount(BigDecimal amount) {
        if (amount == null) {
            this.nonReimbursableAmountInCent = null;
        } else {
            this.nonReimbursableAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }

    public BigDecimal getReimbursableAmount() {
        if (reimbursableAmountInCent == null) {
            return null;
        }
        return new BigDecimal(reimbursableAmountInCent).divide(new BigDecimal(100));
    }

    public void setReimbursableAmount(BigDecimal amount) {
        if (amount == null) {
            this.reimbursableAmountInCent = null;
        } else {
            this.reimbursableAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }

    public BigDecimal getTotalPayableAmount() {
        if (totalPayableAmountInCent == null) {
            return null;
        }
        return new BigDecimal(totalPayableAmountInCent).divide(new BigDecimal(100));
    }

    public void setTotalPayableAmount(BigDecimal amount) {
        if (amount == null) {
            this.totalPayableAmountInCent = null;
        } else {
            this.totalPayableAmountInCent = amount.multiply(new BigDecimal(100)).intValue();
        }
    }
}
