package com.yixun.wid.v2.controller;

import com.mongodb.client.result.DeleteResult;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.bean.in.PriceLimitBatchDeleteIn;
import com.yixun.wid.v2.entity.PriceLimit;
import com.yixun.wid.v2.entity.ThreeCatalogue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 待遇 限价目录管理相关接口
 */
@RestController
@RequestMapping("/v2/pricelimit")
public class PriceLimitController {

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 分页查询限价目录列表
     * @param projectName 项目名称（模糊查询）
     * @param effective 是否有效
     * @param hospitalLevel 医院等级
     * @param threeCatalogueId 三目录ID
     * @param commonPage 分页参数
     * @return 分页结果
     */
    @GetMapping("/list")
    public CommonResult<List<PriceLimit>> list(
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) Boolean effective,
            @RequestParam(required = false) String hospitalLevel,
            @RequestParam(required = false) Long threeCatalogueId,
            CommonPage commonPage) {
        // 构建查询条件
        Query query = new Query();

        // 项目名称模糊查询
        if (StringUtils.hasText(projectName)) {
            query.addCriteria(Criteria.where("projectName").regex(".*" + projectName + ".*", "i"));
        }

        // 有效状态过滤
        if (effective != null) {
            query.addCriteria(Criteria.where("effective").is(effective));
        }
        
        // 医院等级过滤
        if (StringUtils.hasText(hospitalLevel)) {
            query.addCriteria(Criteria.where("priceLimitLevel").regex(".*" + hospitalLevel + ".*", "i"));
        }
        
        // 三目录ID过滤
        if (threeCatalogueId != null) {
            query.addCriteria(Criteria.where("threeCatalogueId").is(threeCatalogueId));
        }

        // 添加排序：按创建时间倒序
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));

        // 设置分页信息
        MongoUtil.setPageInfo(mongoTemplate, PriceLimit.class, query, commonPage);

        // 执行查询
        List<PriceLimit> priceLimits = mongoTemplate.find(query, PriceLimit.class);
        
        // 附加三目录信息并设置有效性状态
        for (PriceLimit priceLimit : priceLimits) {
            if (priceLimit.getThreeCatalogueId() != null) {
                ThreeCatalogue threeCatalogue = mongoTemplate.findById(priceLimit.getThreeCatalogueId(), ThreeCatalogue.class);
                if (threeCatalogue != null) {
                    if (StringUtils.hasText(threeCatalogue.getProjectName())) {
                        priceLimit.setProjectName(threeCatalogue.getProjectName());
                    }
                    // 为PriceLimit的effective状态赋值为ThreeCatalogue的effective字段的值
                    priceLimit.setEffective(threeCatalogue.getEffective());
                }
            }
        }

        return CommonResult.successPageData(priceLimits, commonPage);
    }

    /**
     * 根据ID获取限价目录详情
     * @param id 限价目录ID
     * @return 限价目录详情
     */
    @GetMapping("/detail")
    public CommonResult<PriceLimit> getDetail(@RequestParam Long id) {
        PriceLimit priceLimit = mongoTemplate.findById(id, PriceLimit.class);
        if (priceLimit == null) {
            return CommonResult.failResult(10001, "限价目录不存在");
        }
        
        // 附加三目录信息并设置有效性状态
        if (priceLimit.getThreeCatalogueId() != null) {
            ThreeCatalogue threeCatalogue = mongoTemplate.findById(priceLimit.getThreeCatalogueId(), ThreeCatalogue.class);
            if (threeCatalogue != null) {
                if (StringUtils.hasText(threeCatalogue.getProjectName())) {
                    priceLimit.setProjectName(threeCatalogue.getProjectName());
                }
                // 为PriceLimit的effective状态赋值为ThreeCatalogue的effective字段的值
                priceLimit.setEffective(threeCatalogue.getEffective());
            }
        }
        
        return CommonResult.successData(priceLimit);
    }

    /**
     * 新增限价目录
     * @param priceLimit 限价目录信息
     * @return 新增结果
     */
    @PostMapping("/add")
    public CommonResult<PriceLimit> add(@RequestBody PriceLimit priceLimit) {
        // 验证关联的三目录是否存在
        if (priceLimit.getThreeCatalogueId() != null) {
            ThreeCatalogue threeCatalogue = mongoTemplate.findById(priceLimit.getThreeCatalogueId(), ThreeCatalogue.class);
            if (threeCatalogue == null) {
                return CommonResult.failResult(10001, "关联的三目录不存在");
            }
            // 设置项目名称
            priceLimit.setProjectName(threeCatalogue.getProjectName());
        }
        
        // 生成唯一ID
        priceLimit.setId(SnGeneratorUtil.getId());

        // 设置创建时间和更新时间
        Date now = new Date();
        priceLimit.setCreateTime(now);
        priceLimit.setUpdateTime(now);

        // 保存到数据库
        mongoTemplate.save(priceLimit);

        return CommonResult.successData(priceLimit);
    }

    /**
     * 更新限价目录
     * @param priceLimit 限价目录信息
     * @return 更新结果
     */
    @PostMapping("/update")
    public CommonResult<PriceLimit> update(@RequestBody PriceLimit priceLimit) {
        // 检查ID是否存在
        if (priceLimit.getId() == null) {
            return CommonResult.failResult(10001, "限价目录ID不能为空");
        }

        // 查询原有数据
        PriceLimit existingLimit = mongoTemplate.findById(priceLimit.getId(), PriceLimit.class);
        if (existingLimit == null) {
            return CommonResult.failResult(10001, "限价目录不存在");
        }
        
        // 如果更新了三目录ID，则验证三目录是否存在
        if (priceLimit.getThreeCatalogueId() != null && !priceLimit.getThreeCatalogueId().equals(existingLimit.getThreeCatalogueId())) {
            ThreeCatalogue threeCatalogue = mongoTemplate.findById(priceLimit.getThreeCatalogueId(), ThreeCatalogue.class);
            if (threeCatalogue == null) {
                return CommonResult.failResult(10001, "关联的三目录不存在");
            }
            // 更新项目名称
            priceLimit.setProjectName(threeCatalogue.getProjectName());
        }

        // 设置更新时间
        priceLimit.setUpdateTime(new Date());
        // 保留创建时间
        priceLimit.setCreateTime(existingLimit.getCreateTime());

        // 更新到数据库
        mongoTemplate.save(priceLimit);

        return CommonResult.successData(priceLimit);
    }

    /**
     * 批量删除限价目录
     * @param in 批量删除参数
     * @return 删除结果
     */
    @PostMapping("/batchDelete")
    public CommonResult<Long> batchDelete(@RequestBody PriceLimitBatchDeleteIn in) {
        // 检查ID列表是否为空
        if (in.getIds() == null || in.getIds().isEmpty()) {
            return CommonResult.failResult(10001, "限价目录ID列表不能为空");
        }

        // 构建查询条件
        Query query = new Query(Criteria.where("_id").in(in.getIds()));

        // 执行删除
        DeleteResult result = mongoTemplate.remove(query, PriceLimit.class);

        return CommonResult.successData(result.getDeletedCount());
    }
    
    /**
     * 切换限价目录有效状态
     * @param id 限价目录ID
     * @param effective 要设置的有效状态
     * @return 操作结果
     */
    @PostMapping("/toggleEffective")
    public CommonResult<Void> toggleEffective(@RequestParam Long id, @RequestParam Boolean effective) {
        // 检查限价目录是否存在
        PriceLimit priceLimit = mongoTemplate.findById(id, PriceLimit.class);
        if (priceLimit == null) {
            return CommonResult.failResult(10001, "限价目录不存在");
        }
        
        // 更新有效状态
        priceLimit.setEffective(effective);
        priceLimit.setUpdateTime(new Date());
        
        // 保存更新
        mongoTemplate.save(priceLimit);
        
        return CommonResult.successResult("操作成功");
    }

    /**
     * 查询限价目录列表（不分页）
     * @param projectName 项目名称（模糊查询）
     * @param effective 是否有效
     * @param hospitalLevel 医院等级
     * @param threeCatalogueId 三目录ID
     * @return 限价目录列表
     */
    @GetMapping("/listAll")
    public CommonResult<List<PriceLimit>> listAll(
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) Boolean effective,
            @RequestParam(required = false) String hospitalLevel,
            @RequestParam(required = false) Long threeCatalogueId) {
        // 构建查询条件
        Query query = new Query();

        // 项目名称模糊查询
        if (StringUtils.hasText(projectName)) {
            query.addCriteria(Criteria.where("projectName").regex(".*" + projectName + ".*", "i"));
        }

        // 有效状态过滤
        if (effective != null) {
            query.addCriteria(Criteria.where("effective").is(effective));
        }
        
        // 医院等级过滤
        if (StringUtils.hasText(hospitalLevel)) {
            query.addCriteria(Criteria.where("priceLimitLevel").regex(".*" + hospitalLevel + ".*", "i"));
        }
        
        // 三目录ID过滤
        if (threeCatalogueId != null) {
            query.addCriteria(Criteria.where("threeCatalogueId").is(threeCatalogueId));
        }

        // 添加排序：按创建时间倒序
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));

        // 执行查询
        List<PriceLimit> priceLimits = mongoTemplate.find(query, PriceLimit.class);
        
        // 附加三目录信息并设置有效性状态
        for (PriceLimit priceLimit : priceLimits) {
            if (priceLimit.getThreeCatalogueId() != null) {
                ThreeCatalogue threeCatalogue = mongoTemplate.findById(priceLimit.getThreeCatalogueId(), ThreeCatalogue.class);
                if (threeCatalogue != null) {
                    if (StringUtils.hasText(threeCatalogue.getProjectName())) {
                        priceLimit.setProjectName(threeCatalogue.getProjectName());
                    }
                    // 为PriceLimit的effective状态赋值为ThreeCatalogue的effective字段的值
                    priceLimit.setEffective(threeCatalogue.getEffective());
                }
            }
        }

        return CommonResult.successData(priceLimits);
    }
} 