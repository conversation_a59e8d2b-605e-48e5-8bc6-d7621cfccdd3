server:
  servlet:
    context-path: /work-injury-declare-admin-api
spring:
  jackson:
    time-zone: Asia/Shanghai
  profiles:
    active: ${profile.env}
  application:
    name: work-injury-declare-admin-api
  cloud:
    nacos:
      config:
        # 已经创建好的命名空间，会有一个id
        namespace: longquan-prj

mybatis-plus:
  global-config:
    banner: false
    db-config:
      logic-delete-value: 1
      logic-not-delete-value: 0

filePath: /data/file/

message:
  smsSign: 成都易训企业管理

aliyun:
  expired: 3600
  access-key-id: LTAI5tG6iHn9PH7wFT5fuGKb
  access-key-secret: ******************************
  role-arn: acs:ram::1841673459408468:role/ramossrolenew
  policy-user: |
    {
      "Version": "1",
      "Statement": [
        {
          "Effect": "Allow",
          "Action": "oss:PutObject",
          "Resource": "acs:oss:*:*:*/*"
        },
        {
          "Effect": "Deny",
          "Action": "oss:PutObject",
          "Resource": [
            "acs:oss:*:*:*/*.html",
            "acs:oss:*:*:*/*.HTML",
            "acs:oss:*:*:*/*.Html",
            "acs:oss:*:*:*/*.HTM",
            "acs:oss:*:*:*/*.htm",
            "acs:oss:*:*:*/*.Exe",
            "acs:oss:*:*:*/*.EXE",
            "acs:oss:*:*:*/*.exe",
            "acs:oss:*:*:*/*.bat",
            "acs:oss:*:*:*/*.BAT"
          ]
        }
      ]
    }

api:
  ocrApi: "https://u507501-b1c0-93001905.nmb1.seetacloud.com:8443/Diagnosis"
  generalOcrApi: "http://192.168.252.62:9202/ocr"
  llmsBaseUrl: http://192.168.0.76:6001 #http://100.92.234.30:6001
  # AI接口完整URL配置
  ai:
    querySimilarityUrl: http://192.168.252.62:10002/query_similarity #${api.llmsBaseUrl}/query_similarity
    materialClassificationUrl: ${api.llmsBaseUrl}/material_classification
    acceptedInformationDiagnosisUrl: ${api.llmsBaseUrl}/accepted_Information_diagnosis
    surgicalInformationUrl: ${api.llmsBaseUrl}/surgical_information
    billInformationUrl: ${api.llmsBaseUrl}/bill_information
    billOcrUrl: ${api.llmsBaseUrl}/bill_ocr
    listOcrUrl: ${api.llmsBaseUrl}/list_ocr
    calculatorUrl: ${api.llmsBaseUrl}/calculator
    projectSearchUrl: ${api.llmsBaseUrl}/project_search
    conclusionRecommendUrl: ${api.llmsBaseUrl}/conclusion_recommend
    # 新增的AI接口配置
    materialRecognitionUrl: ${api.llmsBaseUrl}/material_recognition
    materialRecognitionProgressUrl: ${api.llmsBaseUrl}/material_recognition_progress
    materialAuditUrl: ${api.llmsBaseUrl}/material_audit
    caseRecommendationUrl: ${api.llmsBaseUrl}/case_recommendation
    quickFillUrl: ${api.llmsBaseUrl}/quick_fill

jwt:
  secret:
    account: ********************************************************
  expiration: 604800    #单位秒，值为7天‬

weixin:
  page: 1
