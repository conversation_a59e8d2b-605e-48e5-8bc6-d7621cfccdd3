package com.yixun.wid.exception;

import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.common.CommonErrorInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.stream.Collectors;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

	/**
	 * 处理@RequestBody参数校验异常
	 */
	@ExceptionHandler(MethodArgumentNotValidException.class)
	public CommonResult handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
		log.info("MethodArgumentNotValidException: ", e);

		// 获取第一个校验失败的字段错误信息
		FieldError fieldError = e.getBindingResult().getFieldError();
		String message = fieldError != null ? fieldError.getDefaultMessage() : "参数校验失败";

		return CommonResult.failResult(CommonErrorInfo.code_1001, message);
	}

	/**
	 * 处理方法参数校验异常
	 */
	@ExceptionHandler(ConstraintViolationException.class)
	public CommonResult handleConstraintViolationException(ConstraintViolationException e) {
		log.info("ConstraintViolationException: ", e);

		// 获取第一个校验失败的约束违反信息
		String message = e.getConstraintViolations().stream()
			.map(ConstraintViolation::getMessage)
			.collect(Collectors.joining(", "));

		if (message.isEmpty()) {
			message = "参数校验失败";
		}

		return CommonResult.failResult(CommonErrorInfo.code_1001, message);
	}

	/**
	 * 处理表单绑定校验异常
	 */
	@ExceptionHandler(BindException.class)
	public CommonResult handleBindException(BindException e) {
		log.info("BindException: ", e);

		// 获取第一个校验失败的字段错误信息
		FieldError fieldError = e.getBindingResult().getFieldError();
		String message = fieldError != null ? fieldError.getDefaultMessage() : "参数校验失败";

		return CommonResult.failResult(CommonErrorInfo.code_1001, message);
	}

	@ExceptionHandler(DataErrorException.class)
	public CommonResult data(DataErrorException e) {
		log.info("DataErrorException: ", e);
		return CommonResult.failResult(CommonErrorInfo.code_6001, e.getMessage());
	}

	@ExceptionHandler(UnLoginException.class)
	public CommonResult unLogin(UnLoginException e) {
		log.info("UnLoginException: ", e);
		return CommonResult.failResult(CommonErrorInfo.code_4001, e.getMessage());
	}

	@ExceptionHandler(PermissionErrorException.class)
	public CommonResult unLogin(PermissionErrorException e) {
		log.info("PermissionErrorException: ", e);
		return CommonResult.failResult(CommonErrorInfo.code_2001, e.getMessage());
	}

	@ExceptionHandler(ParameterErrorException.class)
	public CommonResult parameter(ParameterErrorException e) {
		log.info("ParameterErrorException: ", e);
		return CommonResult.failResult(CommonErrorInfo.code_1001, e.getMessage());
	}

	@ExceptionHandler(RuntimeException.class)
	public CommonResult runtime(RuntimeException e) {
		log.info("RuntimeException: ", e);
//		return CommonResult.failResult(CommonErrorInfo.code_3001, "系统异常，请联系管理员");
		 return CommonResult.failResult(CommonErrorInfo.code_3001, e.getMessage());
	}

	@ExceptionHandler(ClientAbortException.class)
	public String runtime(ClientAbortException e) {
		log.info("ClientAbortException: ", e);
		return "ClientAbortException:" + e.getMessage();
	}
}
