package com.yixun.wid.v2.vo.medical;

import lombok.Data;
import java.util.List;

/**
 * 批量导入结果
 */
@Data
public class BatchImportResult {
    /**
     * 总记录数
     */
    private Integer totalCount;

    /**
     * 成功导入数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failCount;

    /**
     * 失败项列表
     */
    private List<FailItem> failItems;

    /**
     * 失败行号列表
     */
    private List<Integer> failRowNums;

    /**
     * 相似机构列表
     */
    private List<SimilarInstitutionItem> similarInstitutions;

    /**
     * 错误结果文件下载URL
     */
    private String errorResultUrl;
} 