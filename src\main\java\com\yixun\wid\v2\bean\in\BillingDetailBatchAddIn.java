package com.yixun.wid.v2.bean.in;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 账单明细批量添加输入参数
 */
@Data
public class BillingDetailBatchAddIn {
    
    /**
     * 关联的账单信息ID
     */
    @NotNull(message = "账单信息ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long billingInfoId;
    
    /**
     * 账单明细列表
     */
    @NotEmpty(message = "账单明细列表不能为空")
    @Valid
    private List<BillingDetailItem> billingDetails;
    
    @Data
    public static class BillingDetailItem {
        /**
         * 账单明细ID（用于更新，新增时可为空）
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 项目名称
         */
        private String projectName;
        
        /**
         * 项目编码
         */
        private String projectCode;
        
        /**
         * 费用类别
         */
        private String feeType;
        
        /**
         * 费用分类
         */
        private String feeCategory;
        
        /**
         * 费用等级
         */
        private String feeLevel;
        
        /**
         * 是否工伤
         */
        private Boolean isWorkInjury;
        
        /**
         * 明细在当前账单中的序号
         */
        private Integer orderNum;
        
        /**
         * 数量
         */
        private Integer quantity;
        
        /**
         * 单价(元)
         */
        private java.math.BigDecimal unitPrice;
        
        /**
         * 金额(元)
         */
        private java.math.BigDecimal amount;
        
        /**
         * 不可报销金额(元)
         */
        private java.math.BigDecimal nonReimbursableAmount;
    }
}
