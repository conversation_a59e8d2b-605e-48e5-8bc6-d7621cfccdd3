package com.yixun.wid.utils;

import java.util.regex.Pattern;

/**
 * 密码复杂度校验工具类
 */
public class PasswordComplexityUtil {

    /**
     * 密码复杂度正则表达式
     * 要求：包含字母、数字、特殊字符，且不少于8位
     */
    private static final String PASSWORD_PATTERN = 
        "^(?=.*[a-zA-Z])(?=.*\\d)(?=.*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>/?]).*$";
    
    private static final Pattern pattern = Pattern.compile(PASSWORD_PATTERN);
    
    /**
     * 校验密码复杂度
     * @param password 密码
     * @return true-符合要求，false-不符合要求
     */
    public static boolean isValidPassword(String password) {
        if (password == null || password.length() < 8) {
            return false;
        }
        return pattern.matcher(password).matches();
    }
    
    /**
     * 获取密码复杂度要求说明
     * @return 密码要求说明
     */
    public static String getPasswordRequirement() {
        return "密码必须包含字母、数字、特殊字符的组合，且不少于8位";
    }
}
