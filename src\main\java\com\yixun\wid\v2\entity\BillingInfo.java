package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账单信息实体
 */
@Data
@Document("billingInfo")
public class BillingInfo {

    /**
     * 主键id
     */
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关联的工伤待遇业务ID，多对一关系
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long medicalCasesId;

    /**
     * 治疗医院ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long hospitalId;

	/**
	 * 手动匹配的治疗医院ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long matchHospitalId;

    /**
     * 治疗医院
     */
    private String hospital;

    /**
     * 医院等级
     */
    private List<String> hospitalLevel;

    /**
     * 医院性质
     */
    private String hospitalNature;

    /**
     * 限价医院等级
     */
    private String priceLimitLevel;

    /**
     * 账单号
     */
    private String billId;

    /**
     * 账单数
     */
    private Integer billNum;

    /**
     * 治疗类型 门诊/住院
     */
    private String treatmentType;

    /**
     * 门诊开始时间/入院时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date outpatientStartTime;

    /**
     * 门诊结束时间/出院时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date outpatientEndTime;

    /**
     * 门诊天数/住院天数
     */
    private Integer outpatientDays;

    /**
     * 先期给付类型
     */
    private String advancePaymentType;

    /**
     * 是否追回
     */
    private Boolean isRecovery;

    /**
     * 是否跨统筹区就诊
     */
    private Boolean isCrossPlanningArea;

    /**
     * 电子清单列表，临时字段，不会持久化到数据库
     */
    @Transient
    private List<BillingDetail> billingDetails;

    /**
     * 电子清单分组
     */
    private List<BillingDetailsGroup> billingDetailsGroup;

    /**
     * 就诊日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date visitDate;

    /**
     * 就诊结束日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date visitEndDate;

    /**
     * 出院日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date dischargeDate;

    /**
     * 账单总金额（分）
     */
    @JsonIgnore
    private Integer amountInCent;

    /**
     * 账单总金额（元），接口交互使用的单位为元
     */
    @Transient
    private BigDecimal amount;

    /**
     * 可报销金额（分）
     */
    @JsonIgnore
    private Integer reimbursableAmountInCent;

    /**
     * 可报销金额（元），接口交互使用的单位为元
     */
    @Transient
    private BigDecimal reimbursableAmount;

    /**
     * 不可报销金额（分）
     */
    @JsonIgnore
    private Integer nonReimbursableAmountInCent;

    /**
     * 不可报销金额（元），接口交互使用的单位为元
     */
    @Transient
    private BigDecimal nonReimbursableAmount;

    /**
     * 账单分组总金额(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer totalBillAmountInCent;

    /**
     * 账单分组总金额(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal totalBillAmount;

    /**
     * 账单分组合理费用总额(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer totalReasonableFeeInCent;

    /**
     * 账单分组合理费用总额(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal totalReasonableFee;

    /**
     * 账单分组审核扣减总额(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer totalAuditDeductionInCent;

    /**
     * 账单分组审核扣减总额(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal totalAuditDeduction;

    /**
     * 账单分组非工伤扣减总额(分)，数据库存储的单位为分
     */
    @JsonIgnore
    private Integer totalNonWorkInjuryDeductionInCent;

    /**
     * 账单分组非工伤扣减总额(元)，接口交互使用的单位为元
     */
    @Transient
    private BigDecimal totalNonWorkInjuryDeduction;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;

    public BigDecimal getAmount() {
        if (amountInCent == null) {
            return null;
        }
        return new BigDecimal(amountInCent).divide(new BigDecimal(100));
    }

    public void setAmount(BigDecimal amount) {
        if (amount == null) {
            this.amountInCent = null;
            return;
        }
        this.amountInCent = amount.multiply(new BigDecimal(100)).intValue();
    }

    public BigDecimal getReimbursableAmount() {
        if (reimbursableAmountInCent == null) {
            return null;
        }
        return new BigDecimal(reimbursableAmountInCent).divide(new BigDecimal(100));
    }

    public void setReimbursableAmount(BigDecimal reimbursableAmount) {
        if (reimbursableAmount == null) {
            this.reimbursableAmountInCent = null;
            return;
        }
        this.reimbursableAmountInCent = reimbursableAmount.multiply(new BigDecimal(100)).intValue();
    }

    public BigDecimal getNonReimbursableAmount() {
        if (nonReimbursableAmountInCent == null) {
            return null;
        }
        return new BigDecimal(nonReimbursableAmountInCent).divide(new BigDecimal(100));
    }

    public void setNonReimbursableAmount(BigDecimal nonReimbursableAmount) {
        if (nonReimbursableAmount == null) {
            this.nonReimbursableAmountInCent = null;
            return;
        }
        this.nonReimbursableAmountInCent = nonReimbursableAmount.multiply(new BigDecimal(100)).intValue();
    }

    /**
     * 获取电子清单分组，动态计算合理费用、审核扣减、非工伤扣减字段
     * 基于已存在的billingDetailsGroup对象，根据其feeType筛选billingDetails数据进行计算
     */
    public List<BillingDetailsGroup> getBillingDetailsGroup() {
        if (billingDetailsGroup == null) {
            return new ArrayList<>();
        }

        // 如果没有billingDetails数据，直接返回原始分组
        if (billingDetails == null || billingDetails.isEmpty()) {
            return billingDetailsGroup;
        }

        // 为每个分组计算动态字段
        for (BillingDetailsGroup group : billingDetailsGroup) {
            calculateGroupDynamicFields(group);
        }

        return billingDetailsGroup;
    }

    /**
     * 为分组计算动态字段（合理费用、审核扣减、非工伤扣减）
     * 根据分组的feeType筛选对应的billingDetails进行计算
     */
    private void calculateGroupDynamicFields(BillingDetailsGroup group) {
        if (group.getFeeType() == null) {
            return;
        }

        // 初始化动态字段
        group.setReasonableFee(BigDecimal.ZERO);
        group.setAuditDeduction(BigDecimal.ZERO);
        group.setNonWorkInjuryDeduction(BigDecimal.ZERO);

        // 筛选匹配该分组feeType的明细
        for (BillingDetail detail : billingDetails) {
            if (group.getFeeType().equals(detail.getFeeType())) {
                calculateDeductionsForDetail(group, detail);
            }
        }
    }

    /**
     * 获取账单分组总金额(元)
     * 将数据库中存储的分转换为元
     */
    public BigDecimal getTotalBillAmount() {
        // 先触发分组动态字段的计算
        getBillingDetailsGroup();
        // 然后计算总计字段
        calculateTotalBillAmount();
        
        if (totalBillAmountInCent == null) {
            return null;
        }
        return new BigDecimal(totalBillAmountInCent).divide(new BigDecimal(100));
    }

    /**
     * 设置账单分组总金额(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setTotalBillAmount(BigDecimal totalBillAmount) {
        if (totalBillAmount == null) {
            this.totalBillAmountInCent = null;
            return;
        }
        this.totalBillAmountInCent = totalBillAmount.multiply(new BigDecimal(100)).intValue();
    }

    /**
     * 获取账单分组合理费用总额(元)
     * 将数据库中存储的分转换为元
     */
    public BigDecimal getTotalReasonableFee() {
        // 先触发分组动态字段的计算
        getBillingDetailsGroup();
        // 然后计算总计字段
        calculateTotalReasonableFee();
        
        if (totalReasonableFeeInCent == null) {
            return null;
        }
        return new BigDecimal(totalReasonableFeeInCent).divide(new BigDecimal(100));
    }

    /**
     * 设置账单分组合理费用总额(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setTotalReasonableFee(BigDecimal totalReasonableFee) {
        if (totalReasonableFee == null) {
            this.totalReasonableFeeInCent = null;
            return;
        }
        this.totalReasonableFeeInCent = totalReasonableFee.multiply(new BigDecimal(100)).intValue();
    }

    /**
     * 获取账单分组审核扣减总额(元)
     * 将数据库中存储的分转换为元
     */
    public BigDecimal getTotalAuditDeduction() {
        // 先触发分组动态字段的计算
        getBillingDetailsGroup();
        // 然后计算总计字段
        calculateTotalAuditDeduction();
        
        if (totalAuditDeductionInCent == null) {
            return null;
        }
        return new BigDecimal(totalAuditDeductionInCent).divide(new BigDecimal(100));
    }

    /**
     * 设置账单分组审核扣减总额(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setTotalAuditDeduction(BigDecimal totalAuditDeduction) {
        if (totalAuditDeduction == null) {
            this.totalAuditDeductionInCent = null;
            return;
        }
        this.totalAuditDeductionInCent = totalAuditDeduction.multiply(new BigDecimal(100)).intValue();
    }

    /**
     * 获取账单分组非工伤扣减总额(元)
     * 将数据库中存储的分转换为元
     */
    public BigDecimal getTotalNonWorkInjuryDeduction() {
        // 先触发分组动态字段的计算
        getBillingDetailsGroup();
        // 然后计算总计字段
        calculateTotalNonWorkInjuryDeduction();
        
        if (totalNonWorkInjuryDeductionInCent == null) {
            return null;
        }
        return new BigDecimal(totalNonWorkInjuryDeductionInCent).divide(new BigDecimal(100));
    }

    /**
     * 设置账单分组非工伤扣减总额(元)
     * 将前端传入的元转换为分存入数据库
     */
    public void setTotalNonWorkInjuryDeduction(BigDecimal totalNonWorkInjuryDeduction) {
        if (totalNonWorkInjuryDeduction == null) {
            this.totalNonWorkInjuryDeductionInCent = null;
            return;
        }
        this.totalNonWorkInjuryDeductionInCent = totalNonWorkInjuryDeduction.multiply(new BigDecimal(100)).intValue();
    }

    /**
     * 计算账单分组总金额
     * 基于billingDetailsGroup中的billAmount计算总和
     */
    public void calculateTotalBillAmount() {
        if (billingDetailsGroup == null || billingDetailsGroup.isEmpty()) {
            this.totalBillAmountInCent = null;
            return;
        }

        int totalAmount = 0;
        for (BillingDetailsGroup group : billingDetailsGroup) {
            if (group.getBillAmountInCent() != null) {
                totalAmount += group.getBillAmountInCent();
            }
        }

        this.totalBillAmountInCent = totalAmount;
    }

    /**
     * 计算账单分组合理费用总额
     * 基于billingDetailsGroup中的reasonableFee计算总和
     */
    public void calculateTotalReasonableFee() {
        if (billingDetailsGroup == null || billingDetailsGroup.isEmpty()) {
            this.totalReasonableFeeInCent = null;
            return;
        }

        int totalAmount = 0;
        for (BillingDetailsGroup group : billingDetailsGroup) {
            if (group.getReasonableFeeInCent() != null) {
                totalAmount += group.getReasonableFeeInCent();
            }
        }

        this.totalReasonableFeeInCent = totalAmount;
    }

    /**
     * 计算账单分组审核扣减总额
     * 基于billingDetailsGroup中的auditDeduction计算总和
     */
    public void calculateTotalAuditDeduction() {
        if (billingDetailsGroup == null || billingDetailsGroup.isEmpty()) {
            this.totalAuditDeductionInCent = null;
            return;
        }

        int totalAmount = 0;
        for (BillingDetailsGroup group : billingDetailsGroup) {
            if (group.getAuditDeductionInCent() != null) {
                totalAmount += group.getAuditDeductionInCent();
            }
        }

        this.totalAuditDeductionInCent = totalAmount;
    }

    /**
     * 计算账单分组非工伤扣减总额
     * 基于billingDetailsGroup中的nonWorkInjuryDeduction计算总和
     */
    public void calculateTotalNonWorkInjuryDeduction() {
        if (billingDetailsGroup == null || billingDetailsGroup.isEmpty()) {
            this.totalNonWorkInjuryDeductionInCent = null;
            return;
        }

        int totalAmount = 0;
        for (BillingDetailsGroup group : billingDetailsGroup) {
            if (group.getNonWorkInjuryDeductionInCent() != null) {
                totalAmount += group.getNonWorkInjuryDeductionInCent();
            }
        }

        this.totalNonWorkInjuryDeductionInCent = totalAmount;
    }

    /**
     * 计算所有总计字段
     * 依次调用各个总计字段的计算方法
     */
    public void calculateAllTotals() {
        calculateTotalBillAmount();
        calculateTotalReasonableFee();
        calculateTotalAuditDeduction();
        calculateTotalNonWorkInjuryDeduction();
    }

    /**
     * 为单个明细计算扣减金额
     */
    private void calculateDeductionsForDetail(BillingDetailsGroup group, BillingDetail detail) {
        if (detail.getAmount() == null) {
            return;
        }

        BigDecimal detailAmount = detail.getAmount();
        BigDecimal nonReimbursableAmount = detail.getNonReimbursableAmount();

        // 如果没有不可报销金额，说明是合理费用
        if (nonReimbursableAmount == null || nonReimbursableAmount.compareTo(BigDecimal.ZERO) == 0) {
            group.setReasonableFee(group.getReasonableFee().add(detailAmount));
            return;
        }

        // 根据扣减类型分类
        String deductionType = detail.getDeductionType();
        if ("审核扣减".equals(deductionType)) {
            // 审核扣减：不可报销金额计入审核扣减，剩余部分计入合理费用
            group.setAuditDeduction(group.getAuditDeduction().add(nonReimbursableAmount));
            BigDecimal reasonableAmount = detailAmount.subtract(nonReimbursableAmount);
            if (reasonableAmount.compareTo(BigDecimal.ZERO) > 0) {
                group.setReasonableFee(group.getReasonableFee().add(reasonableAmount));
            }
        } else if ("非工伤扣减".equals(deductionType)) {
            // 非工伤扣减：整个金额都计入非工伤扣减
            group.setNonWorkInjuryDeduction(group.getNonWorkInjuryDeduction().add(detailAmount));
        } else {
            // 其他情况：有不可报销金额但没有明确扣减类型，按审核扣减处理
            group.setAuditDeduction(group.getAuditDeduction().add(nonReimbursableAmount));
            BigDecimal reasonableAmount = detailAmount.subtract(nonReimbursableAmount);
            if (reasonableAmount.compareTo(BigDecimal.ZERO) > 0) {
                group.setReasonableFee(group.getReasonableFee().add(reasonableAmount));
            }
        }
    }
}
