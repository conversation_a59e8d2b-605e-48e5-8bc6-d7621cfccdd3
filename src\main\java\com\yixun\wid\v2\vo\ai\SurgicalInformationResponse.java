package com.yixun.wid.v2.vo.ai;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.List;

/**
 * 手术信息响应结果
 */
@Data
public class SurgicalInformationResponse {

    /**
     * 状态
     */
    private String status;

    /**
     * 数据
     */
    private SurgicalData data;

    /**
     * 消息
     */
    private String message;

    @Data
    public static class SurgicalData {
        /**
         * 手术名称列表
         */
        @JSONField(name = "surgical_name")
        private List<String> surgicalName;
    }
}
