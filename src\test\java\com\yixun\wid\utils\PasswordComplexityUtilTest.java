package com.yixun.wid.utils;


import org.junit.jupiter.api.Test;

import static org.springframework.test.util.AssertionErrors.*;

/**
 * 密码复杂度校验工具类测试
 */
public class PasswordComplexityUtilTest {

    @Test
    public void testValidPasswords() {
        // 测试有效密码
        assertTrue("包含字母、数字、特殊字符且8位", PasswordComplexityUtil.isValidPassword("Abc123!@"));
        assertTrue("包含字母、数字、特殊字符且超过8位", PasswordComplexityUtil.isValidPassword("Password123!@#"));
        assertTrue("包含大小写字母、数字、特殊字符", PasswordComplexityUtil.isValidPassword("MyPass123!"));
    }

    @Test
    public void testInvalidPasswords() {
        // 测试无效密码
        assertFalse("长度不足8位", PasswordComplexityUtil.isValidPassword("Ab1!"));
        assertFalse("只包含字母和数字", PasswordComplexityUtil.isValidPassword("Password123"));
        assertFalse("只包含字母和特殊字符", PasswordComplexityUtil.isValidPassword("Password!@#"));
        assertFalse("只包含数字和特殊字符", PasswordComplexityUtil.isValidPassword("123456!@#"));
        assertFalse("只包含字母", PasswordComplexityUtil.isValidPassword("Password"));
        assertFalse("只包含数字", PasswordComplexityUtil.isValidPassword("12345678"));
        assertFalse("只包含特殊字符", PasswordComplexityUtil.isValidPassword("!@#$%^&*"));
        assertFalse("空密码", PasswordComplexityUtil.isValidPassword(""));
        assertFalse("null密码", PasswordComplexityUtil.isValidPassword(null));
    }

    @Test
    public void testPasswordRequirement() {
        String requirement = PasswordComplexityUtil.getPasswordRequirement();
        assertNotNull("密码要求说明不能为空", requirement);
        assertTrue("密码要求说明应包含关键信息",
            requirement.contains("字母") &&
            requirement.contains("数字") &&
            requirement.contains("特殊字符") &&
            requirement.contains("8位"));
    }
}
