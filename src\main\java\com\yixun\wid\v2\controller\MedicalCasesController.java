package com.yixun.wid.v2.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import com.yixun.wid.bean.common.CommonErrorInfo;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.entity.User;
import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.UserService;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.bean.in.BillingInfoIn;
import com.yixun.wid.v2.entity.*;
import com.yixun.wid.v2.enums.UserType;
import com.yixun.wid.v2.service.BillingInfoService;
import com.yixun.wid.v2.service.MedicalCasesLogService;
import com.yixun.wid.utils.RedisKeyResolver;
import com.yixun.wid.v2.utils.AiUtils;
import com.yixun.wid.v2.utils.ClaimsDataUtil;
import com.yixun.wid.v2.utils.ServletRequestUtils;
import com.yixun.wid.v2.vo.BatchReceiveResult;
import com.yixun.wid.v2.vo.BillDuplicateCheckRequest;
import com.yixun.wid.v2.vo.BillDuplicateCheckResponse;
import com.yixun.wid.v2.vo.MaterialClassificationProgress;
import com.yixun.wid.v2.vo.MaterialClassificationRequestVO;
import com.yixun.wid.v2.vo.UpdateMaterialsRequest;
import com.yixun.wid.v2.vo.ai.AcceptedInformationDiagnosisRequest;
import com.yixun.wid.v2.vo.ai.AcceptedInformationDiagnosisResponse;
import com.yixun.wid.v2.vo.ai.AcceptedInformationDiagnosisVO;
import com.yixun.wid.v2.vo.ai.BillInformationResponse;
import com.yixun.wid.v2.vo.ai.BillInformationVO;
import com.yixun.wid.v2.vo.ai.BillOcrRequest;
import com.yixun.wid.v2.vo.ai.ListOcrRequest;
import com.yixun.wid.v2.vo.ai.ListOcrResponse;
import com.yixun.wid.v2.vo.ai.ListOcrVO;
import com.yixun.wid.v2.vo.ai.SurgicalInformationVO;
import com.yixun.wid.v2.vo.ai.CalculatorRequest;
import com.yixun.wid.v2.vo.ai.CalculatorResponse;
import com.yixun.wid.v2.vo.ai.ListOcrRequest;
import com.yixun.wid.v2.vo.ai.ListOcrResponse;
import com.yixun.wid.v2.vo.ai.MaterialClassificationResponse;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import com.yixun.wid.v2.vo.ai.ProjectSearchRequest;
import com.yixun.wid.v2.vo.ai.ProjectSearchResponse;
import com.yixun.wid.v2.vo.ai.ProjectSearchSimpleResponse;
import com.yixun.wid.v2.vo.ai.SurgicalInformationResponse;
import com.yixun.wid.v2.bean.in.ProjectFeeSimilarSearchIn;
import com.yixun.wid.v2.service.ProjectFeeService;
import com.yixun.wid.v2.utils.ThreeCatalogueSearchUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 待遇 工伤待遇业务相关接口
 */
@Slf4j
@RequestMapping("/v2/medical/cases")
@RestController
public class MedicalCasesController {

	@Resource
	private MongoTemplate mongoTemplate;

	@Resource
	private AdministratorService administratorService;

	@Resource
	private UserService userService;

	@Resource
	private BillingInfoService billingInfoService;

	@Resource
	private MedicalCasesLogService medicalCasesLogService;

	@Resource
	private ClaimsDataUtil claimsDataUtil;

	@Resource
	private AiUtils aiUtils;

	@Resource
	private ProjectFeeService projectFeeService;

	@Resource
	private ThreeCatalogueSearchUtils threeCatalogueSearchUtils;

	@Resource
	private ThreadPoolTaskExecutor executor;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	/**
	 * 获取当前用户名
	 *
	 * @return 当前用户名
	 */
	public String getUserName() {
		Long userId = ServletRequestUtils.getUserId();
		UserType userType = ServletRequestUtils.getUserType();
		if (UserType.ADMIN.equals(userType)) {
			Administrator administratorById = administratorService.getAdministratorById(userId);
			return administratorById.getRealName();
		}
		if (UserType.USER.equals(userType)) {
			User userById = userService.getUserById(userId);
			return userById.getRealName();
		}
		return null;
	}

	/**
	 * 保存单个工伤待遇业务
	 *
	 * @param medicalCases 工伤待遇业务信息
	 * @return 保存后的工伤待遇业务信息
	 */
	@PostMapping("/save")
	public CommonResult<MedicalCases> saveMedicalCases(@Validated @RequestBody MedicalCases medicalCases) {

		// 设置基本信息
		medicalCases.setId(SnGeneratorUtil.getId());
		Date now = new Date();
		medicalCases.setCreateTime(now);
		medicalCases.setUpdateTime(now);

		// 设置创建用户信息
		Long recentUserId = ServletRequestUtils.getUserId();
		String recentUserName = getUserName();
		medicalCases.setCreateUserId(String.valueOf(recentUserId));
		medicalCases.setCreateUserName(recentUserName);

		// 设置当前操作用户信息，初始时与创建用户相同
		medicalCases.setRecentUserId(String.valueOf(recentUserId));
		medicalCases.setRecentUserName(recentUserName);

		medicalCases.setStatus(MedicalCasesStatus.Applying);

		// 删除此处设置案件编号的代码

		// 如果包含理算信息，则设置责任项目名称
		if (medicalCases.getClaimsInformations() != null && !medicalCases.getClaimsInformations().isEmpty()) {
			claimsDataUtil.setAllResponsibilityItems(medicalCases);
		}

		// 保存工伤待遇业务
		mongoTemplate.save(medicalCases);

		// 保存关联的账单信息
		saveRelatedBillingInfos(medicalCases.getId(), medicalCases.getBillingInfos());

		// 记录操作日志
		MedicalCasesLog log = new MedicalCasesLog();
		log.setMedicalCasesId(medicalCases.getId());
		log.setStatus("新建受理");
		log.setUserId(String.valueOf(recentUserId));
		log.setUserName(recentUserName);
		log.setCreateTime(now);
		medicalCasesLogService.add(log);

		// 如果isSubmit为true，则在保存之后调用更新方法
		if (Boolean.TRUE.equals(medicalCases.getIsSubmit())) {
			return updateMedicalCases(medicalCases);
		}

		return CommonResult.successData(medicalCases);
	}

	/**
	 * 批量保存工伤待遇业务
	 *
	 * @param list 工伤待遇业务列表
	 * @return 操作结果
	 */
	@PostMapping("/batch/save")
	public CommonResult<Void> batchSaveMedicalCases(@RequestBody List<MedicalCases> list) {
		// 获取当前用户信息
		Long recentUserId = ServletRequestUtils.getUserId();
		String recentUserName = getUserName();
		Date now = new Date();

		// 删除按受理日期分组的代码

		for (MedicalCases mc : list) {


			// 设置ID和时间
			mc.setId(SnGeneratorUtil.getId());
			mc.setCreateTime(now);
			mc.setUpdateTime(now);

			// 设置创建用户信息
			mc.setCreateUserId(String.valueOf(recentUserId));
			mc.setCreateUserName(recentUserName);

			// 设置当前操作用户信息
			mc.setRecentUserId(String.valueOf(recentUserId));
			mc.setRecentUserName(recentUserName);

			// 删除分组代码
		}

		// 删除设置案件编号的代码

		// 批量保存工伤待遇业务
		mongoTemplate.insertAll(list);

		// 保存关联的账单信息
		for (MedicalCases mc : list) {
			saveRelatedBillingInfos(mc.getId(), mc.getBillingInfos());

			// 记录操作日志
			MedicalCasesLog log = new MedicalCasesLog();
			log.setMedicalCasesId(mc.getId());
			log.setStatus("新建受理");
			log.setUserId(String.valueOf(recentUserId));
			log.setUserName(recentUserName);
			log.setCreateTime(now);
			medicalCasesLogService.add(log);
		}

		return CommonResult.successResult("操作成功");
	}

	/**
	 * 校验初审状态所需的必填参数
	 *
	 * @param medicalCases 工伤待遇业务信息
	 * @return 校验失败返回错误信息，校验通过返回null
	 */
	private String validatePreReviewingParams(MedicalCases medicalCases) {
		// 基本信息校验
		if (medicalCases.getAcceptDate() == null) {
			return "受理日期不能为空";
		}
		if (medicalCases.getAccidentDate() == null) {
			return "事故日期不能为空";
		}
		if (StrUtil.isBlank(medicalCases.getTreatmentType())) {
			return "治疗类型不能为空";
		}

		if (StrUtil.isBlank(medicalCases.getWorkerName())) {
			return "职工姓名不能为空";
		}
		if (StrUtil.isBlank(medicalCases.getGender())) {
			return "性别不能为空";
		}
		if (StrUtil.isBlank(medicalCases.getIdCard())) {
			return "身份证号码不能为空";
		}
		if (medicalCases.getHasInsurance() == null) {
			return "是否参保不能为空";
		}
		if (StrUtil.isBlank(medicalCases.getInsuranceAddress())) {
			return "参保地不能为空";
		}
		if (medicalCases.getBusinessTypes() == null || medicalCases.getBusinessTypes().isEmpty()) {
			return "申请业务不能为空";
		}

		// 医疗信息校验
		if (medicalCases.getInjuryDiagnoses() == null || medicalCases.getInjuryDiagnoses().isEmpty()) {
			return "临床诊断不能为空";
		}
		if (medicalCases.getSurgeryInfos() == null || medicalCases.getSurgeryInfos().isEmpty()) {
			return "手术信息不能为空";
		}

		// 账单信息校验
		if (medicalCases.getBillingInfos() == null || medicalCases.getBillingInfos().isEmpty()) {
			return "账单列表不能为空";
		}

		// 校验每个账单的详细信息
		for (int i = 0; i < medicalCases.getBillingInfos().size(); i++) {
			BillingInfo billingInfo = medicalCases.getBillingInfos().get(i);
			String prefix = "第" + (i + 1) + "个账单";

			// 校验账单基本信息
			if (StrUtil.isBlank(billingInfo.getHospital())) {
				return prefix + "的治疗医院不能为空";
			}
			if (StrUtil.isBlank(billingInfo.getBillId())) {
				return prefix + "的账单号不能为空";
			}
			if (StrUtil.isBlank(billingInfo.getTreatmentType())) {
				return prefix + "的治疗类型不能为空";
			}
			if (billingInfo.getOutpatientStartTime() == null) {
				return prefix + "的门诊开始时间不能为空";
			}
			if (billingInfo.getOutpatientEndTime() == null) {
				return prefix + "的门诊结束时间不能为空";
			}
			if (billingInfo.getOutpatientDays() == null) {
				return prefix + "的门诊天数不能为空";
			}
			if (billingInfo.getIsCrossPlanningArea() == null) {
				return prefix + "的是否跨统筹区就诊不能为空";
			}
			if (billingInfo.getIsRecovery() == null) {
				return prefix + "的是否追回不能为空";
			}

			// 校验电子清单分组
			if (billingInfo.getBillingDetailsGroup() == null || billingInfo.getBillingDetailsGroup().isEmpty()) {
				return prefix + "的电子清单分组不能为空";
			}

			for (int j = 0; j < billingInfo.getBillingDetailsGroup().size(); j++) {
				BillingDetailsGroup group = billingInfo.getBillingDetailsGroup().get(j);
				String groupPrefix = prefix + "第" + (j + 1) + "个电子清单分组";

				if (StrUtil.isBlank(group.getCode())) {
					return groupPrefix + "的编码不能为空";
				}
				if (StrUtil.isBlank(group.getFeeType())) {
					return groupPrefix + "的费用项目不能为空";
				}
				if (group.getBillAmount() == null) {
					return groupPrefix + "的账单金额不能为空";
				}
			}
		}

		return null;
	}



	/**
	 * 保存关联的账单信息和账单明细（支持新增、更新、删除）
	 *
	 * @param medicalCasesId 工伤待遇业务ID
	 * @param billingInfos   账单信息列表
	 */
	private void saveRelatedBillingInfos(Long medicalCasesId, List<BillingInfo> billingInfos) {
		Date now = new Date();

		// 查询数据库中已存在的账单信息
		Query existingBillingInfoQuery = Query.query(Criteria.where("medicalCasesId").is(medicalCasesId));
		List<BillingInfo> existingBillingInfos = mongoTemplate.find(existingBillingInfoQuery, BillingInfo.class);

		// 收集传入的账单信息ID
		Set<Long> incomingBillingInfoIds = new HashSet<>();
		if (billingInfos != null) {
			for (BillingInfo billingInfo : billingInfos) {
				if (billingInfo.getId() != null) {
					incomingBillingInfoIds.add(billingInfo.getId());
				}
			}
		}

		// 删除数据库中存在但传入列表中不存在的账单信息及其明细
		for (BillingInfo existingInfo : existingBillingInfos) {
			if (!incomingBillingInfoIds.contains(existingInfo.getId())) {
				// 先删除关联的账单明细
				Query deleteDetailQuery = Query.query(Criteria.where("billingInfoId").is(existingInfo.getId()));
				mongoTemplate.remove(deleteDetailQuery, BillingDetail.class);

				// 再删除账单信息
				mongoTemplate.remove(existingInfo);
			}
		}

		// 处理传入的账单信息
		if (billingInfos != null && !billingInfos.isEmpty()) {
			List<BillingInfo> billingInfosToSave = new ArrayList<>();
			List<BillingDetail> allBillingDetailsToSave = new ArrayList<>();

			for (BillingInfo billingInfo : billingInfos) {
				boolean isUpdate = billingInfo.getId() != null;

				if (isUpdate) {
					// 更新现有账单信息
					BillingInfo existingInfo = mongoTemplate.findById(billingInfo.getId(), BillingInfo.class);
					if (existingInfo != null) {
						// 保留创建时间，更新其他字段
						billingInfo.setCreateTime(existingInfo.getCreateTime());
						billingInfo.setUpdateTime(now);
						billingInfo.setMedicalCasesId(medicalCasesId);
					}
				} else {
					// 新增账单信息
					billingInfo.setId(SnGeneratorUtil.getId());
					billingInfo.setMedicalCasesId(medicalCasesId);
					billingInfo.setCreateTime(now);
					billingInfo.setUpdateTime(now);
				}

				// 处理账单明细
				processBillingDetails(billingInfo, now, allBillingDetailsToSave);

				// 清空临时字段，避免保存到账单信息中
				billingInfo.setBillingDetails(null);
				billingInfosToSave.add(billingInfo);
			}

			// 批量保存账单信息
			for (BillingInfo billingInfo : billingInfosToSave) {
				mongoTemplate.save(billingInfo);
			}

			// 批量保存账单明细
			for (BillingDetail billingDetail : allBillingDetailsToSave) {
				mongoTemplate.save(billingDetail);
			}
		}
	}

	/**
	 * 处理账单明细的新增、更新、删除
	 *
	 * @param billingInfo 账单信息
	 * @param now 当前时间
	 * @param allBillingDetailsToSave 待保存的账单明细列表
	 */
	private void processBillingDetails(BillingInfo billingInfo, Date now, List<BillingDetail> allBillingDetailsToSave) {
		Long billingInfoId = billingInfo.getId();
		List<BillingDetail> incomingDetails = billingInfo.getBillingDetails();

		// 查询数据库中已存在的账单明细
		Query existingDetailQuery = Query.query(Criteria.where("billingInfoId").is(billingInfoId));
		List<BillingDetail> existingDetails = mongoTemplate.find(existingDetailQuery, BillingDetail.class);

		// 收集传入的账单明细ID
		Set<Long> incomingDetailIds = new HashSet<>();
		if (incomingDetails != null) {
			for (BillingDetail detail : incomingDetails) {
				if (detail.getId() != null) {
					incomingDetailIds.add(detail.getId());
				}
			}
		}

		// 删除数据库中存在但传入列表中不存在的账单明细
		for (BillingDetail existingDetail : existingDetails) {
			if (!incomingDetailIds.contains(existingDetail.getId())) {
				mongoTemplate.remove(existingDetail);
			}
		}

		// 处理传入的账单明细
		if (incomingDetails != null && !incomingDetails.isEmpty()) {
			for (int i = 0; i < incomingDetails.size(); i++) {
				BillingDetail billingDetail = incomingDetails.get(i);
				boolean isUpdate = billingDetail.getId() != null;

				if (isUpdate) {
					// 更新现有账单明细
					BillingDetail existingDetail = mongoTemplate.findById(billingDetail.getId(), BillingDetail.class);
					if (existingDetail != null) {
						// 保留创建时间，更新其他字段
						billingDetail.setCreateTime(existingDetail.getCreateTime());
						billingDetail.setUpdateTime(now);
						billingDetail.setBillingInfoId(billingInfoId);
					}
				} else {
					// 新增账单明细
					billingDetail.setId(SnGeneratorUtil.getId());
					billingDetail.setBillingInfoId(billingInfoId);
					billingDetail.setCreateTime(now);
					billingDetail.setUpdateTime(now);
				}

				// 如果没有设置序号，则自动设置
				if (billingDetail.getOrderNum() == null) {
					billingDetail.setOrderNum(i + 1);
				}

				// 设置是否工伤，如果为空默认设置为是
				if (billingDetail.getIsWorkInjury() == null) {
					billingDetail.setIsWorkInjury(true);
				}

				// 基于三目录相似搜索设置费用等级
				threeCatalogueSearchUtils.setFeeLevelAndThreeCatalogueIdBySearch(billingDetail);

				// 自动计算金额、数量、单价
				billingDetail.autoCalculate();

				allBillingDetailsToSave.add(billingDetail);
			}
		}
	}

	/**
	 * 更新工伤待遇业务
	 *
	 * @param medicalCases 工伤待遇业务信息
	 * @return 更新后的工伤待遇业务信息
	 */
	@PostMapping("/update")
	public CommonResult<MedicalCases> updateMedicalCases(@Validated @RequestBody MedicalCases medicalCases) {
		if (medicalCases.getId() == null) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "ID不能为空");
		}


		MedicalCases existing = mongoTemplate.findById(medicalCases.getId(), MedicalCases.class);
		if (existing == null) {
			return CommonResult.failResult(CommonErrorInfo.code_6001, "数据不存在");
		}
		MedicalCasesStatus oldStatus = existing.getStatus();
		// 新增：assigneeUserId为空不能更新
		if (!MedicalCasesStatus.Applying.equals(oldStatus)
				&& (existing.getAssigneeUserId() == null || existing.getAssigneeUserId().isEmpty())) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "未分配任务不能更新");
		}

		// 判断是否为提交操作（由isSubmit字段决定）
		boolean isSubmit = Boolean.TRUE.equals(medicalCases.getIsSubmit());
		MedicalCasesStatus status = oldStatus;
		if (isSubmit) {
			// 状态流转到下一阶段
			status = getNextStatus(oldStatus);
			medicalCases.setStatus(status);

			// 当案件状态为Applying且isSubmit为true时，设置案件编号（表示正式受理）
			if (MedicalCasesStatus.Applying.equals(oldStatus)) {
				Date acceptDate = medicalCases.getAcceptDate();
				if (acceptDate == null) {
					acceptDate = new Date(); // 如果未设置受理日期，使用当前日期
					medicalCases.setAcceptDate(acceptDate);
				}

				// 按照格式生成案件编号：BX1014 + 受理日期 + 序号
				String dateFormat = cn.hutool.core.date.DateUtil.format(acceptDate, "yyyyMMdd");
				String prefix = "BX1014" + dateFormat;

				// 查询当天最大序号
				Query query = new Query();
				query.addCriteria(Criteria.where("caseNumber").regex("^" + prefix));
				query.with(Sort.by(Sort.Direction.DESC, "caseNumber"));
				query.limit(1);

				MedicalCases lastCase = mongoTemplate.findOne(query, MedicalCases.class);

				int sequence = 1;
				if (lastCase != null && lastCase.getCaseNumber() != null) {
					try {
						// 提取序号部分并加1
						String sequenceStr = lastCase.getCaseNumber().substring(prefix.length());
						sequence = Integer.parseInt(sequenceStr) + 1;
					} catch (Exception e) {
						log.error("解析案件编号序号失败", e);
					}
				}

				// 格式化为3位数，不足前面补0
				String sequenceFormat = String.format("%03d", sequence);
				medicalCases.setCaseNumber(prefix + sequenceFormat);
			}
		} else {
			// 仅保存，状态不变
			medicalCases.setStatus(oldStatus);
		}

		// 校验（如有需要）
		if (isSubmit) {
			String errorMessage = validatePreReviewingParams(medicalCases);
			if (errorMessage != null) {
				return CommonResult.failResult(CommonErrorInfo.code_1001, errorMessage);
			}
			// 设置提交用户信息
			Long recentUserId = ServletRequestUtils.getUserId();
			String recentUserName = getUserName();
			medicalCases.setSubmitUserId(String.valueOf(recentUserId));
			medicalCases.setSubmitUserName(recentUserName);
			// 根据新状态记录对应用户信息
			Date now = new Date();
			if (MedicalCasesStatus.PreReviewing.equals(status)) {
				medicalCases.setAcceptUserId(String.valueOf(recentUserId));
				medicalCases.setAcceptUserName(recentUserName);
				medicalCases.setAcceptTime(now);
			} else if (MedicalCasesStatus.Reviewing.equals(status)) {
				medicalCases.setPreReviewUserId(String.valueOf(recentUserId));
				medicalCases.setPreReviewUserName(recentUserName);
				medicalCases.setPreReviewTime(now);
			} else if (MedicalCasesStatus.FinalReviewing.equals(status)) {
				medicalCases.setReviewUserId(String.valueOf(recentUserId));
				medicalCases.setReviewUserName(recentUserName);
				medicalCases.setReviewTime(now);
			} else if (MedicalCasesStatus.Done.equals(status)) {
				medicalCases.setFinalReviewUserId(String.valueOf(recentUserId));
				medicalCases.setFinalReviewUserName(recentUserName);
				medicalCases.setFinalReviewTime(now);
			}
		}

		// 保存账单信息
		List<BillingInfo> billingInfos = medicalCases.getBillingInfos();
		if (billingInfos != null) {
			// billingInfos不为null时才进行操作
			// 如果为空数组，则清空案件对应的所有账单信息
			// 如果不为空，则按正常逻辑保存/更新账单信息
			saveRelatedBillingInfos(medicalCases.getId(), billingInfos);

			// 清空临时字段，不保存到数据库
			medicalCases.setBillingInfos(null);
		}
		// 如果billingInfos为null，则不操作现有的账单信息

		// 使用属性复制，但排除createUserId、createUserName和createTime
		org.springframework.beans.BeanUtils.copyProperties(medicalCases, existing, "createUserId", "createUserName",
				"createTime");

		// 如果包含理算信息，则设置责任项目名称
		if (existing.getClaimsInformations() != null && !existing.getClaimsInformations().isEmpty()) {
			claimsDataUtil.setAllResponsibilityItems(existing);
		}

		// 如果是提交操作，确保assigneeUserId被清空
		if (isSubmit) {
			existing.setAssigneeUserId(null);
		}

		// 设置更新时间
		Date now = new Date();
		existing.setUpdateTime(now);

		// 设置当前操作用户信息
		Long recentUserId = ServletRequestUtils.getUserId();
		String recentUserName = getUserName();
		existing.setRecentUserId(String.valueOf(recentUserId));
		existing.setRecentUserName(recentUserName);

		// 保存更新后的工伤待遇业务
		mongoTemplate.save(existing);

		// 记录操作日志
		MedicalCasesLog log = new MedicalCasesLog();
		log.setMedicalCasesId(existing.getId());
		log.setStatus("受理修改");
		log.setUserId(String.valueOf(recentUserId));
		log.setUserName(recentUserName);
		log.setCreateTime(now);
		medicalCasesLogService.add(log);

		return CommonResult.successData(existing);
	}

	/**
	 * 查询工伤待遇业务列表 前端暂时忽略该接口
	 *
	 * @param name         工伤待遇业务名称（模糊搜索，可选）
	 * @param organization 组织名称（模糊搜索，可选）
	 * @param idCard       身份证号（模糊搜索，可选）
	 * @param statusList   业务状态列表（可选，支持多个状态同时筛选）业务受理（Applying）待接收任务（PreReviewing、Reviewing、FinalReviewing）
	 * @param submitTime   提交时间
	 * @param commonPage   分页信息
	 * @return 工伤待遇业务列表（分页）
	 */
	@GetMapping("/list")
	public CommonResult<List<MedicalCases>> list(@RequestParam(required = false) String name,
			@RequestParam(required = false) String organization,
			@RequestParam(required = false) String idCard,
			@RequestParam(required = false) List<MedicalCasesStatus> statusList,
			@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date submitTime,
			CommonPage commonPage) {
		Query query = new Query();
		if (StrUtil.isNotBlank(name)) {
			query.addCriteria(Criteria.where("name").regex(".*" + name + ".*", "i"));
		}
		if (StrUtil.isNotBlank(organization)) {
			query.addCriteria(Criteria.where("organization").regex(".*" + organization + ".*", "i"));
		}
		if (StrUtil.isNotBlank(idCard)) {
			query.addCriteria(Criteria.where("idCard").regex(".*" + idCard + ".*", "i"));
		}
		if (statusList != null && !statusList.isEmpty()) {
			query.addCriteria(Criteria.where("status").in(statusList));
		}
		// 按创建时间倒序排序
		query.with(Sort.by(Sort.Direction.DESC, "createTime"));
		com.yixun.wid.utils.MongoUtil.setPageInfo(mongoTemplate, MedicalCases.class, query, commonPage);
		List<MedicalCases> list = mongoTemplate.find(query, MedicalCases.class);

		// 为每个工伤待遇业务加载关联的账单信息
		for (MedicalCases medicalCases : list) {
			loadBillingInfos(medicalCases);
		}

		return CommonResult.successPageData(list, commonPage);
	}

	/**
	 * 根据ID获取工伤待遇业务详情，包含关联的账单信息
	 *
	 * @param id 工伤待遇业务ID
	 * @return 工伤待遇业务详情，包含关联的账单信息
	 */
	@GetMapping("/detail")
	public CommonResult<MedicalCases> getById(@RequestParam Long id) {
		MedicalCases medicalCases = mongoTemplate.findById(id, MedicalCases.class);
		if (medicalCases == null) {
			return CommonResult.failResult(10001, "工伤待遇业务不存在");
		}

		// 加载关联的账单信息
		loadBillingInfos(medicalCases);

		return CommonResult.successData(medicalCases);
	}

	/**
	 * 为工伤待遇业务加载关联的账单信息
	 *
	 * @param medicalCases 工伤待遇业务
	 */
	private void loadBillingInfos(MedicalCases medicalCases) {
		// 查询关联到该工伤待遇业务的账单信息
		Query billingInfoQuery = Query.query(Criteria.where("medicalCasesId").is(medicalCases.getId()));
		billingInfoQuery.with(Sort.by(Sort.Direction.DESC, "createTime"));
		List<BillingInfo> billingInfoList = mongoTemplate.find(billingInfoQuery, BillingInfo.class);

		if (!billingInfoList.isEmpty()) {
			// 遍历每个账单信息，加载其关联的账单明细
			for (BillingInfo billingInfo : billingInfoList) {
				// 查询关联的账单明细
				Query detailQuery = Query.query(Criteria.where("billingInfoId").is(billingInfo.getId()));
				detailQuery.with(Sort.by(Sort.Direction.ASC, "orderNum"));
				List<BillingDetail> details = mongoTemplate.find(detailQuery, BillingDetail.class);

				// 设置账单明细到账单信息的临时字段中
				billingInfo.setBillingDetails(details);
			}

			// 设置账单信息列表到工伤待遇业务中
			medicalCases.setBillingInfos(billingInfoList);
		} else {
			medicalCases.setBillingInfos(new ArrayList<>());
		}
	}

	/**
	 * 导入工伤待遇业务数据
	 *
	 * @return 操作结果
	 */
	@PostMapping("/import")
	public CommonResult<Void> importMedicalCases() {
		return CommonResult.successResult("操作成功");
	}

	/**
	 * 批量接收工伤待遇业务任务
	 *
	 * @param ids 工伤待遇业务ID列表
	 * @return 操作结果，包含成功接收数量和无法接收的案件列表
	 */
	@PostMapping("/batch/receive")
	public CommonResult<BatchReceiveResult> batchReceiveMedicalCases(@RequestBody List<Long> ids) {
		if (ids == null || ids.isEmpty()) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "ID列表不能为空");
		}
		Long recentUserId = ServletRequestUtils.getUserId();
		String recentUserName = getUserName();
		Date now = new Date();
		List<MedicalCases> toUpdate = new ArrayList<>();
		List<BatchReceiveResult.FailedCase> cannotReceiveList = new ArrayList<>();

		for (Long id : ids) {
			MedicalCases mc = mongoTemplate.findById(id, MedicalCases.class);
			if (mc != null) {
				// 检查案件是否已被他人接收
				if (mc.getAssigneeUserId() != null && !mc.getAssigneeUserId().isEmpty()) {
					BatchReceiveResult.FailedCase failInfo = new BatchReceiveResult.FailedCase(
							String.valueOf(mc.getId()),
							"案件已被他人接收");
					cannotReceiveList.add(failInfo);
					continue;
				}

				// 检查是否为待复审案件，且当前用户是该案件的初审人员
				if (MedicalCasesStatus.Reviewing.equals(mc.getStatus())) {
					// 直接使用MedicalCases中的preReviewUserId字段判断
					if (String.valueOf(recentUserId).equals(mc.getPreReviewUserId())) {
						BatchReceiveResult.FailedCase failInfo = new BatchReceiveResult.FailedCase(
								String.valueOf(mc.getId()),
								"不能复审自己初审的案件");
						cannotReceiveList.add(failInfo);
						continue;
					}
				}

				mc.setAssigneeUserId(String.valueOf(recentUserId));
				mc.setRecentUserId(String.valueOf(recentUserId));
				mc.setRecentUserName(recentUserName);
				mc.setUpdateTime(now);
				toUpdate.add(mc);
			}
		}

		// 保存成功接收的案件
		if (!toUpdate.isEmpty()) {
			for (MedicalCases mc : toUpdate) {
				mongoTemplate.save(mc);
			}
		}

		// 构建返回结果
		BatchReceiveResult result = new BatchReceiveResult(
				toUpdate.size(),
				cannotReceiveList.size(),
				cannotReceiveList);

		return CommonResult.successData(result);
	}

	/**
	 * 业务受理列表查询接口，只查Applying状态
	 *
	 * @param name            职工姓名（模糊搜索，可选）
	 * @param organization    单位名称（模糊搜索，可选）
	 * @param idCard          身份证号（模糊搜索，可选）
	 * @param acceptDateStart 受理时间开始（可选）
	 * @param acceptDateEnd   受理时间结束（可选）
	 * @param commonPage      分页信息
	 * @return 工伤待遇业务列表（分页）
	 */
	@GetMapping("/apply-list")
	public CommonResult<List<MedicalCases>> applyList(@RequestParam(required = false) String name,
			@RequestParam(required = false) String organization,
			@RequestParam(required = false) String idCard,
			@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateStart,
			@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateEnd,
			CommonPage commonPage) {
		Query query = new Query();
		// 只查Applying状态
		query.addCriteria(Criteria.where("status").is(MedicalCasesStatus.Applying));
		if (StrUtil.isNotBlank(name)) {
			query.addCriteria(Criteria.where("workerName").regex(".*" + name + ".*", "i"));
		}
		if (StrUtil.isNotBlank(organization)) {
			query.addCriteria(Criteria.where("organization").regex(".*" + organization + ".*", "i"));
		}
		if (StrUtil.isNotBlank(idCard)) {
			query.addCriteria(Criteria.where("idCard").regex(".*" + idCard + ".*", "i"));
		}
		// 受理时间范围筛选
		if (acceptDateStart != null || acceptDateEnd != null) {
			Criteria dateCriteria = Criteria.where("acceptDate");
			if (acceptDateStart != null && acceptDateEnd != null) {
				dateCriteria.gte(acceptDateStart).lte(acceptDateEnd);
			} else if (acceptDateStart != null) {
				dateCriteria.gte(acceptDateStart);
			} else if (acceptDateEnd != null) {
				dateCriteria.lte(acceptDateEnd);
			}
			query.addCriteria(dateCriteria);
		}
		// 按创建时间倒序排序
		query.with(Sort.by(Sort.Direction.DESC, "createTime"));
		com.yixun.wid.utils.MongoUtil.setPageInfo(mongoTemplate, MedicalCases.class, query, commonPage);
		List<MedicalCases> list = mongoTemplate.find(query, MedicalCases.class);
		for (MedicalCases medicalCases : list) {
			loadBillingInfos(medicalCases);
		}
		return CommonResult.successPageData(list, commonPage);
	}

	/**
	 * 待接收任务列表查询接口，只允许筛选PreReviewing、Reviewing、FinalReviewing三种状态
	 *
	 * @param name            职工姓名（模糊搜索，可选）
	 * @param organization    单位名称（模糊搜索，可选）
	 * @param idCard          身份证号（模糊搜索，可选）
	 * @param acceptDateStart 受理时间开始（可选）
	 * @param acceptDateEnd   受理时间结束（可选）
	 * @param statusList      状态筛选（可选，仅允许PreReviewing、Reviewing、FinalReviewing）
	 * @param commonPage      分页信息
	 * @return 工伤待遇业务列表（分页）
	 */
	@GetMapping("/pending-receive-list")
	public CommonResult<List<MedicalCases>> pendingReceiveList(@RequestParam(required = false) String name,
			@RequestParam(required = false) String organization,
			@RequestParam(required = false) String idCard,
			@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateStart,
			@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateEnd,
			@RequestParam(required = false) List<MedicalCasesStatus> statusList,
			CommonPage commonPage) {
		// 允许的状态
		List<MedicalCasesStatus> allowedStatus = java.util.Arrays.asList(MedicalCasesStatus.PreReviewing,
				MedicalCasesStatus.Reviewing, MedicalCasesStatus.FinalReviewing);
		List<MedicalCasesStatus> queryStatus;
		if (statusList == null || statusList.isEmpty()) {
			queryStatus = allowedStatus;
		} else {
			// 只保留允许的状态
			queryStatus = statusList.stream().filter(allowedStatus::contains).collect(Collectors.toList());
			if (queryStatus.isEmpty()) {
				// 如果传入的都不合法，默认查全部允许的
				queryStatus = allowedStatus;
			}
		}
		Query query = new Query();
		query.addCriteria(Criteria.where("status").in(queryStatus));
		// 只查assigneeUserId为空的数据
		query.addCriteria(new Criteria().orOperator(
				Criteria.where("assigneeUserId").is(null),
				Criteria.where("assigneeUserId").is("")));
		if (StrUtil.isNotBlank(name)) {
			query.addCriteria(Criteria.where("workerName").regex(".*" + name + ".*", "i"));
		}
		if (StrUtil.isNotBlank(organization)) {
			query.addCriteria(Criteria.where("organization").regex(".*" + organization + ".*", "i"));
		}
		if (StrUtil.isNotBlank(idCard)) {
			query.addCriteria(Criteria.where("idCard").regex(".*" + idCard + ".*", "i"));
		}
		// 受理时间范围筛选
		if (acceptDateStart != null || acceptDateEnd != null) {
			Criteria dateCriteria = Criteria.where("acceptDate");
			if (acceptDateStart != null && acceptDateEnd != null) {
				dateCriteria.gte(acceptDateStart).lte(acceptDateEnd);
			} else if (acceptDateStart != null) {
				dateCriteria.gte(acceptDateStart);
			} else if (acceptDateEnd != null) {
				dateCriteria.lte(acceptDateEnd);
			}
			query.addCriteria(dateCriteria);
		}
		// 按创建时间倒序排序
		query.with(Sort.by(Sort.Direction.DESC, "createTime"));
		com.yixun.wid.utils.MongoUtil.setPageInfo(mongoTemplate, MedicalCases.class, query, commonPage);
		List<MedicalCases> list = mongoTemplate.find(query, MedicalCases.class);
		for (MedicalCases medicalCases : list) {
			loadBillingInfos(medicalCases);
		}
		return CommonResult.successPageData(list, commonPage);
	}

	/**
	 * 我的任务列表接口，只查assigneeUserId为当前用户ID的数据
	 *
	 * @param name            职工姓名（模糊搜索，可选）
	 * @param organization    单位名称（模糊搜索，可选）
	 * @param idCard          身份证号（模糊搜索，可选）
	 * @param acceptDateStart 受理时间开始（可选）
	 * @param acceptDateEnd   受理时间结束（可选）
	 * @param statusList      状态筛选（可选）
	 * @param commonPage      分页信息
	 * @return 工伤待遇业务列表（分页）
	 */
	@GetMapping("/my-task-list")
	public CommonResult<List<MedicalCases>> myTaskList(@RequestParam(required = false) String name,
			@RequestParam(required = false) String organization,
			@RequestParam(required = false) String idCard,
			@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateStart,
			@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateEnd,
			@RequestParam(required = false) List<MedicalCasesStatus> statusList,
			CommonPage commonPage) {
		Long recentUserId = ServletRequestUtils.getUserId();
		Query query = new Query();
		query.addCriteria(Criteria.where("assigneeUserId").is(String.valueOf(recentUserId)));
		if (StrUtil.isNotBlank(name)) {
			query.addCriteria(Criteria.where("workerName").regex(".*" + name + ".*", "i"));
		}
		if (StrUtil.isNotBlank(organization)) {
			query.addCriteria(Criteria.where("organization").regex(".*" + organization + ".*", "i"));
		}
		if (StrUtil.isNotBlank(idCard)) {
			query.addCriteria(Criteria.where("idCard").regex(".*" + idCard + ".*", "i"));
		}
		// 受理时间范围筛选
		if (acceptDateStart != null || acceptDateEnd != null) {
			Criteria dateCriteria = Criteria.where("acceptDate");
			if (acceptDateStart != null && acceptDateEnd != null) {
				dateCriteria.gte(acceptDateStart).lte(acceptDateEnd);
			} else if (acceptDateStart != null) {
				dateCriteria.gte(acceptDateStart);
			} else if (acceptDateEnd != null) {
				dateCriteria.lte(acceptDateEnd);
			}
			query.addCriteria(dateCriteria);
		}
		if (statusList != null && !statusList.isEmpty()) {
			query.addCriteria(Criteria.where("status").in(statusList));
		}
		// 按创建时间倒序排序
		query.with(Sort.by(Sort.Direction.DESC, "createTime"));
		com.yixun.wid.utils.MongoUtil.setPageInfo(mongoTemplate, MedicalCases.class, query, commonPage);
		List<MedicalCases> list = mongoTemplate.find(query, MedicalCases.class);
		for (MedicalCases medicalCases : list) {
			loadBillingInfos(medicalCases);
		}
		return CommonResult.successPageData(list, commonPage);
	}

	/**
	 * 材料类型识别接口
	 * 使用线程池异步调用AI材料分类方法，并通过Redis跟踪进度
	 *
	 * @param request 材料分类请求参数
	 * @return 操作结果
	 */
	@PostMapping("/material-classification")
	public CommonResult<String> materialClassification(
			@Validated @RequestBody MaterialClassificationRequestVO request) {
		// 生成Redis key
		String progressKey = RedisKeyResolver.getMaterialClassificationProgressKey(request.getId());

		// 初始化进度状态为处理中
		MaterialClassificationProgress progress = MaterialClassificationProgress.processing();
		stringRedisTemplate.opsForValue().set(progressKey, JSON.toJSONString(progress), 30, TimeUnit.MINUTES);

		log.info("材料分类任务开始，业务ID: {}, 文件数量: {}, 类型: {}",
				request.getId(), request.getFiles().size(), request.getType());

		// 使用线程池异步执行材料分类
		CompletableFuture<MaterialClassificationResponse> future = CompletableFuture.supplyAsync(() -> {
			try {
				log.info("开始执行材料分类，业务ID: {}, 文件数量: {}, 类型: {}",
						request.getId(), request.getFiles().size(), request.getType());

				// 调用AI工具进行材料分类
				MaterialClassificationResponse response = aiUtils.materialClassification(request.getFiles(),
						request.getType());

				log.info("材料分类完成，业务ID: {}, 分类结果: {}", request.getId(), response.getStatus());
				return response;
			} catch (Exception e) {
				log.error("材料分类异步执行失败，业务ID: {}", request.getId(), e);
				throw new RuntimeException("材料分类执行失败: " + e.getMessage(), e);
			}
		}, executor);

		// 异步任务完成后更新Redis进度状态
		future.whenComplete((result, throwable) -> {
			try {
				// 检查Redis缓存是否还存在，如果不存在说明任务已被取消
				String currentProgressJson = stringRedisTemplate.opsForValue().get(progressKey);
				if (currentProgressJson == null) {
					log.info("材料分类任务已被取消，跳过进度状态更新，业务ID: {}", request.getId());
					return;
				}

				if (throwable != null) {
					// 失败时更新进度状态
					MaterialClassificationProgress failureProgress = MaterialClassificationProgress
							.failure(throwable.getMessage());
					stringRedisTemplate.opsForValue().set(progressKey, JSON.toJSONString(failureProgress), 30,
							TimeUnit.MINUTES);
					log.error("材料分类异步任务执行失败，业务ID: {}", request.getId(), throwable);
				} else {
					// 成功时更新进度状态
					MaterialClassificationProgress successProgress = MaterialClassificationProgress.success(result);
					stringRedisTemplate.opsForValue().set(progressKey, JSON.toJSONString(successProgress), 30,
							TimeUnit.MINUTES);
					log.info("材料分类异步任务执行成功，业务ID: {}, 状态: {}", request.getId(), result.getStatus());

					// 基于结果更新案件中材料的文件类型
					if ("success".equals(result.getStatus()) && result.getData() != null) {
						try {
							updateMaterialFileTypes(request.getCaseId(), result.getData(), request.getType());
							log.info("成功更新案件材料文件类型，业务ID: {}, 类型: {}", request.getId(), request.getType());
						} catch (Exception e) {
							log.error("更新案件材料文件类型失败，业务ID: {}, 类型: {}", request.getId(), request.getType(), e);
						}
					}
				}
			} catch (Exception e) {
				log.error("更新材料分类进度状态失败，业务ID: {}", request.getId(), e);
			}
		});

		return CommonResult.successData("材料分类任务已提交，正在异步处理中");
	}

	/**
	 * 基于AI识别结果更新案件中材料的文件类型
	 *
	 * @param caseId 案件ID
	 * @param classificationData AI识别的分类结果
	 * @param type 请求类型：application/medical
	 */
	private void updateMaterialFileTypes(Long caseId, MaterialClassificationResponse.ClassificationData classificationData, String type) {
		// 查询案件信息
		MedicalCases medicalCases = mongoTemplate.findById(caseId, MedicalCases.class);
		if (medicalCases == null) {
			log.warn("案件不存在，无法更新材料类型，案件ID: {}", caseId);
			return;
		}

		if (medicalCases.getTreatmentMaterials() == null || medicalCases.getTreatmentMaterials().isEmpty()) {
			log.warn("案件中没有待遇材料，无法更新材料类型，案件ID: {}", caseId);
			return;
		}

		// 创建文件URL到类型的映射
		Map<String, String> fileTypeMap = new HashMap<>();

		// 根据请求类型处理不同的材料分类结果
		if ("medical".equals(type) && classificationData.getMedical() != null) {
			// 处理就诊材料
			processMedicalMaterials(fileTypeMap, classificationData.getMedical());
		} else if ("application".equals(type) && classificationData.getApplication() != null) {
			// 处理申请材料
			processApplicationMaterials(fileTypeMap, classificationData.getApplication());
		} else {
			log.warn("未找到对应类型的分类结果，案件ID: {}, 类型: {}", caseId, type);
			return;
		}

		// 更新文件类型
		boolean hasUpdate = false;
		for (TreatmentMaterials treatmentMaterial : medicalCases.getTreatmentMaterials()) {
			if (treatmentMaterial.getFileList() != null) {
				for (TreatmentMaterials.TreatmentFile file : treatmentMaterial.getFileList()) {
					if (file.getUrl() != null && fileTypeMap.containsKey(file.getUrl())) {
						String newFileType = fileTypeMap.get(file.getUrl());
						if (!newFileType.equals(file.getFileType())) {
							file.setFileType(newFileType);
							hasUpdate = true;
							log.info("更新文件类型：{} -> {}", file.getUrl(), newFileType);
						}
					}
				}
			}
		}

		// 如果有更新，保存到数据库
		if (hasUpdate) {
			medicalCases.setUpdateTime(new Date());
			mongoTemplate.save(medicalCases);
			log.info("成功保存案件材料类型更新，案件ID: {}", caseId);
		} else {
			log.info("没有需要更新的文件类型，案件ID: {}", caseId);
		}
	}

	/**
	 * 处理就诊材料分类结果
	 *
	 * @param fileTypeMap 文件类型映射
	 * @param medicalMaterials 就诊材料分类结果
	 */
	private void processMedicalMaterials(Map<String, String> fileTypeMap, MaterialClassificationResponse.MedicalMaterials medicalMaterials) {
		// 处理病历大类
		if (medicalMaterials.getMedicalRecord() != null) {
			addFilesToTypeMap(fileTypeMap, medicalMaterials.getMedicalRecord().getMedicalRecordSubcategory(), "病历");
			addFilesToTypeMap(fileTypeMap, medicalMaterials.getMedicalRecord().getMedicalConditionCertificate(), "病情证明书");
			addFilesToTypeMap(fileTypeMap, medicalMaterials.getMedicalRecord().getDischargeCertificate(), "出院证明书");
			addFilesToTypeMap(fileTypeMap, medicalMaterials.getMedicalRecord().getDischargeRecord(), "出院记录");
			addFilesToTypeMap(fileTypeMap, medicalMaterials.getMedicalRecord().getInpatientMedicalRecordFirstPage(), "住院病案首页");
			addFilesToTypeMap(fileTypeMap, medicalMaterials.getMedicalRecord().getDiagnosisCertificate(), "诊断证明书");
			addFilesToTypeMap(fileTypeMap, medicalMaterials.getMedicalRecord().getAdmissionCertificate(), "入院证");
			addFilesToTypeMap(fileTypeMap, medicalMaterials.getMedicalRecord().getAdmissionRecord(), "入院记录");
			addFilesToTypeMap(fileTypeMap, medicalMaterials.getMedicalRecord().getPrescriptionSlip(), "处方签");
			addFilesToTypeMap(fileTypeMap, medicalMaterials.getMedicalRecord().getTemperatureSheet(), "体温单");
			addFilesToTypeMap(fileTypeMap, medicalMaterials.getMedicalRecord().getProgressNote(), "病程记录");
		}

		// 处理其他就诊材料类型
		addFilesToTypeMap(fileTypeMap, medicalMaterials.getSurgicalRecord(), "手术记录");
		addFilesToTypeMap(fileTypeMap, medicalMaterials.getCertificate(), "合格证");
		addFilesToTypeMap(fileTypeMap, medicalMaterials.getList(), "清单");
		addFilesToTypeMap(fileTypeMap, medicalMaterials.getElectronicInvoice(), "电子发票");
		addFilesToTypeMap(fileTypeMap, medicalMaterials.getNonElectronicInvoice(), "非电子发票");
		addFilesToTypeMap(fileTypeMap, medicalMaterials.getRecallNotice(), "追回单");
		addFilesToTypeMap(fileTypeMap, medicalMaterials.getExaminationReport(), "检查报告");
		addFilesToTypeMap(fileTypeMap, medicalMaterials.getDoctorOrderSheet(), "医嘱单");
		addFilesToTypeMap(fileTypeMap, medicalMaterials.getOtherConsultationReports(), "其他就诊报告");
		addFilesToTypeMap(fileTypeMap, medicalMaterials.getOtherUnidentified(), "其他未识别");
	}

	/**
	 * 处理申请材料分类结果
	 *
	 * @param fileTypeMap 文件类型映射
	 * @param applicationMaterials 申请材料分类结果
	 */
	private void processApplicationMaterials(Map<String, String> fileTypeMap, MaterialClassificationResponse.ApplicationMaterials applicationMaterials) {
		addFilesToTypeMap(fileTypeMap, applicationMaterials.getWorkInjuryBenefitApplicationForm(), "工伤待遇申请表");
		addFilesToTypeMap(fileTypeMap, applicationMaterials.getWorkInjuryMedicalRehabilitationBenefitApplicationForm(), "工伤医疗(康复)待遇申请表");
		addFilesToTypeMap(fileTypeMap, applicationMaterials.getWorkInjuryDeterminationDocument(), "工伤决定书");
		addFilesToTypeMap(fileTypeMap, applicationMaterials.getWorkInjuryReceiptList(), "工伤收件清单");
		addFilesToTypeMap(fileTypeMap, applicationMaterials.getIdCard(), "身份证");
		addFilesToTypeMap(fileTypeMap, applicationMaterials.getSocialSecurityCard(), "社会保障卡");
		addFilesToTypeMap(fileTypeMap, applicationMaterials.getBankCard(), "银行卡");
		addFilesToTypeMap(fileTypeMap, applicationMaterials.getInitialReexaminationAppraisalConclusionDocument(), "初次(复查)鉴定结论书");
		addFilesToTypeMap(fileTypeMap, applicationMaterials.getNonTaxRevenueGeneralPaymentDocument(), "非税收入一般缴款书");

		// 处理其他材料，统一设置为"其他未识别"
		if (applicationMaterials.getOther() != null && !applicationMaterials.getOther().isEmpty()) {
			for (MaterialClassificationResponse.OtherMaterial otherMaterial : applicationMaterials.getOther()) {
				if (otherMaterial.getFile() != null) {
					fileTypeMap.put(otherMaterial.getFile(), "其他未识别");
				}
			}
		}
	}

	/**
	 * 将文件URL列表添加到类型映射中
	 *
	 * @param fileTypeMap 文件类型映射
	 * @param fileUrls 文件URL列表
	 * @param typeName 类型名称
	 */
	private void addFilesToTypeMap(Map<String, String> fileTypeMap, List<String> fileUrls, String typeName) {
		if (fileUrls != null && !fileUrls.isEmpty()) {
			for (String fileUrl : fileUrls) {
				if (fileUrl != null && !fileUrl.trim().isEmpty()) {
					fileTypeMap.put(fileUrl, typeName);
				}
			}
		}
	}

	/**
	 * 查询材料分类进度
	 *
	 * @param id 业务ID
	 * @return 材料分类进度
	 */
	@GetMapping("/material-classification-progress")
	public CommonResult<MaterialClassificationProgress> getMaterialClassificationProgress(@RequestParam String id) {
		String progressKey = RedisKeyResolver.getMaterialClassificationProgressKey(id);
		String progressJson = stringRedisTemplate.opsForValue().get(progressKey);

		if (progressJson == null) {
			// 未找到进度信息时返回code为-2的进度对象
			MaterialClassificationProgress notFoundProgress = MaterialClassificationProgress.notFound();
			return CommonResult.successData(notFoundProgress);
		}

		MaterialClassificationProgress progress = JSON.parseObject(progressJson, MaterialClassificationProgress.class);
		return CommonResult.successData(progress);
	}

	/**
	 * 取消材料分类任务
	 *
	 * @param id 业务ID
	 * @return 操作结果
	 */
	@PostMapping("/material-classification-cancel")
	public CommonResult<Void> cancelMaterialClassification(@RequestParam String id) {
		String progressKey = RedisKeyResolver.getMaterialClassificationProgressKey(id);
		// 删除Redis缓存
		stringRedisTemplate.delete(progressKey);
		log.info("材料分类任务已取消，业务ID: {}", id);
		return CommonResult.successResult("材料分类任务已取消");
	}

	/**
	 * 根据工伤待遇业务ID和责任树编码查询对应的理算条信息
	 * 如果不存在对应的理算信息，则自动构造一个默认的理算信息
	 * 当历史赔付次数字段初始化或为null时，需基于当前案件的身份证号，查询所有身份证号相同且案件状态为已办结的案件，
	 * 并统计其中责任树编码与当前查询的责任树编码相同且已理算的案件数量，然后将该数量赋值给历史赔付次数字段
	 * 当历史给付天数字段初始化或为null时，需基于当前案件的身份证号，查询所有身份证号相同且案件状态为已办结的案件，
	 * 并统计其中责任树编码与当前查询的责任树编码（只有住院费用责任条需要计算历史给付天数）相同且已理算的案件的共计给付天数之和，然后将该数量赋值给历史给付天数字段
	 * 当历史赔付金额字段初始化或为null时，需基于当前案件的身份证号，查询所有身份证号相同且案件状态为已办结的案件，
	 * 并统计其中责任树编码与当前查询的责任树编码相同且已理算的案件的实际支付金额合计结果，然后将该数量赋值给历史赔付金额字段
	 * 当共计给付天数字段初始化或为null时，需统计当前案件的账单里面的住院天数（仅住院费用责任条需要计算），然后将该数量赋值给共计给付天数字段
	 * 当住院伙食补助金额字段初始化或为null时，需基于当前案件的补助标准和共计给付天数计算住院伙食补助金额（仅住院费用责任条需要计算），然后将该数量赋值给住院伙食补助金额字段
	 * 当发票总金额字段初始化或为null时，需统计当前案件的所有住院/门诊发票（案件关联的账单）总金额合计金额，选择住院责任条的，自动计算所有住院发票的账单总金额；选择门诊责任条的，同理，然后将该数量赋值给发票总金额字段
	 *
	 * @param id   工伤待遇业务ID
	 * @param code 责任树编码
	 * @return 理算条信息
	 */
	@GetMapping("/claims-information")
	public CommonResult<MedicalCases.ClaimsInformation> getClaimsInformation(@RequestParam Long id,
			@RequestParam String code) {
		// 查询医疗案例
		MedicalCases medicalCases = mongoTemplate.findById(id, MedicalCases.class);
		if (medicalCases == null) {
			return CommonResult.failResult(CommonErrorInfo.code_6001, "工伤待遇业务不存在");
		}

		// 获取理算信息Map
		Map<String, MedicalCases.ClaimsInformation> claimsInformations = medicalCases.getClaimsInformations();
		MedicalCases.ClaimsInformation claimsInformation = null;
		boolean isNewClaimsInfo = false;

		// 如果Map存在且包含对应编码的理算条信息，直接返回
		if (claimsInformations != null && !claimsInformations.isEmpty()) {
			claimsInformation = claimsInformations.get(code);
		}

		// 如果不存在对应编码的理算条信息，则构造一个默认的理算信息
		if (claimsInformation == null) {
			claimsInformation = createDefaultClaimsInformation(code);
			isNewClaimsInfo = true;

			// 调用工具类设置责任项目名称
			claimsDataUtil.setResponsibilityItem(claimsInformation);

			// 如果MedicalCases的claimsInformations为空，初始化它
			if (claimsInformations == null) {
				claimsInformations = new HashMap<>();
				medicalCases.setClaimsInformations(claimsInformations);
			}

			// 将构造的理算条信息放入Map中并保存到数据库
			claimsInformations.put(code, claimsInformation);
		}

		// 只在理算信息刚创建时（初始化时）计算历史赔付次数、历史给付天数和历史赔付金额
		if (isNewClaimsInfo && claimsInformation != null && claimsInformation.getClaimsData() != null) {
			// 获取当前案件的身份证号
			String idCard = medicalCases.getIdCard();
			if (StrUtil.isNotBlank(idCard)) {
				// 查询所有身份证号相同且案件状态为已办结的案件
				Query historyQuery = new Query();
				historyQuery.addCriteria(Criteria.where("idCard").is(idCard));
				historyQuery.addCriteria(Criteria.where("status").is(MedicalCasesStatus.Done));
				List<MedicalCases> historyCases = mongoTemplate.find(historyQuery, MedicalCases.class);
				
				// 统计其中责任树编码与当前查询的责任树编码相同且已理算的案件数量
				int count = 0;
				// 统计其中责任树编码与当前查询的责任树编码相同且已理算的案件的共计给付天数之和（仅住院费用责任条）
				int totalDays = 0;
				// 统计其中责任树编码与当前查询的责任树编码相同且已理算的案件的实际支付金额合计结果
				int totalAmount = 0;
				
				for (MedicalCases historyCase : historyCases) {
					// 排除当前案件
					if (historyCase.getId().equals(id)) {
						continue;
					}
					
					Map<String, MedicalCases.ClaimsInformation> historyClaimsInformations = historyCase.getClaimsInformations();
					if (historyClaimsInformations != null && !historyClaimsInformations.isEmpty()) {
						MedicalCases.ClaimsInformation historyClaimsInfo = historyClaimsInformations.get(code);
						if (historyClaimsInfo != null &&
							historyClaimsInfo.getClaimsData() != null &&
							Boolean.TRUE.equals(historyClaimsInfo.getClaimsStatus())) {
							count++;
							
							// 只有住院费用责任条需要计算历史给付天数
							if ("HOSPITALIZATION_COST".equals(code) && historyClaimsInfo.getClaimsData().getTotalPayDays() != null) {
								totalDays += historyClaimsInfo.getClaimsData().getTotalPayDays();
							}
							
							// 计算历史赔付金额
							if (historyClaimsInfo.getClaimsResult() != null && historyClaimsInfo.getClaimsResult().getActualPayAmountInCent() != null) {
								totalAmount += historyClaimsInfo.getClaimsResult().getActualPayAmountInCent();
							}
						}
					}
				}
				
				// 将统计的数量赋值给历史赔付次数字段
				claimsInformation.getClaimsData().setHistoryPayCount(count);
				
				// 将统计的天数赋值给历史给付天数字段（仅住院费用责任条）
				if ("HOSPITALIZATION_COST".equals(code)) {
					claimsInformation.getClaimsData().setHistoryPayDays(totalDays);
				}
				
				// 将统计的金额赋值给历史赔付金额字段
				claimsInformation.getClaimsData().setHistoryPayAmountInCent(totalAmount);
			}
			
			// 计算当前案件的共计给付天数（仅住院费用责任条）
			if ("HOSPITALIZATION_COST".equals(code)) {
				// 查询当前案件的所有住院账单
				Query billingQuery = new Query();
				billingQuery.addCriteria(Criteria.where("medicalCasesId").is(id));
				billingQuery.addCriteria(Criteria.where("treatmentType").is("住院"));
				List<BillingInfo> billingInfos = mongoTemplate.find(billingQuery, BillingInfo.class);
				
				// 统计所有住院账单的住院天数合计
				int currentTotalDays = 0;
				// 统计所有住院账单的总金额合计
				int invoiceTotalAmount = 0;
				// 统计所有住院账单的数量
				int invoiceCount = billingInfos.size();
				// 统计所有住院账单的不可报销金额合计
				int nonReimbursableAmount = 0;
				
				for (BillingInfo billingInfo : billingInfos) {
					if (billingInfo.getOutpatientDays() != null) {
						currentTotalDays += billingInfo.getOutpatientDays();
					}
					if (billingInfo.getAmountInCent() != null) {
						invoiceTotalAmount += billingInfo.getAmountInCent();
					}
					
					// 查询当前账单的所有明细
					Query detailQuery = new Query();
					detailQuery.addCriteria(Criteria.where("billingInfoId").is(billingInfo.getId()));
					List<BillingDetail> billingDetails = mongoTemplate.find(detailQuery, BillingDetail.class);
					
					// 统计当前账单的所有明细的不可报销金额合计
					for (BillingDetail billingDetail : billingDetails) {
						if (billingDetail.getNonReimbursableAmountInCent() != null) {
							nonReimbursableAmount += billingDetail.getNonReimbursableAmountInCent();
						}
					}
				}
				
				// 将统计的天数赋值给共计给付天数字段
				claimsInformation.getClaimsData().setTotalPayDays(currentTotalDays);
				
				// 计算住院伙食补助金额 = 补助标准 * 共计给付天数
				if (claimsInformation.getClaimsData().getFoodAllowanceStandardInCent() != null && currentTotalDays > 0) {
					int foodAllowanceAmount = claimsInformation.getClaimsData().getFoodAllowanceStandardInCent() * currentTotalDays;
					claimsInformation.getClaimsData().setFoodAllowanceAmountInCent(foodAllowanceAmount);
				}
				
				// 将统计的金额赋值给发票总金额字段
				claimsInformation.getClaimsData().setInvoiceTotalAmountInCent(invoiceTotalAmount);
				
				// 将统计的数量赋值给发票张数字段
				claimsInformation.getClaimsData().setInvoiceCount(invoiceCount);
				
				// 将统计的金额赋值给不可报销金额字段
				claimsInformation.getClaimsData().setNonReimbursableAmountInCent(nonReimbursableAmount);
				
				// 统计所有住院账单的账单分组合理费用总额
				int totalReasonableFee = 0;
				for (BillingInfo billingInfo : billingInfos) {
					if (billingInfo.getTotalReasonableFeeInCent() != null) {
						totalReasonableFee += billingInfo.getTotalReasonableFeeInCent();
					}
				}
				
				// 将统计的合理费用总额赋值给可报销金额字段
				claimsInformation.getClaimsData().setReimbursableAmountInCent(totalReasonableFee);
				
				// 计算应付总金额 = 可报销金额 + 住院伙食补助金额
				int totalPayableAmount = totalReasonableFee;
				if (claimsInformation.getClaimsData().getFoodAllowanceAmountInCent() != null) {
					totalPayableAmount += claimsInformation.getClaimsData().getFoodAllowanceAmountInCent();
				}
				claimsInformation.getClaimsData().setTotalPayableAmountInCent(totalPayableAmount);
			} else if ("OUTPATIENT_COST".equals(code)) {
				// 查询当前案件的所有门诊账单
				Query billingQuery = new Query();
				billingQuery.addCriteria(Criteria.where("medicalCasesId").is(id));
				billingQuery.addCriteria(Criteria.where("treatmentType").is("门诊"));
				List<BillingInfo> billingInfos = mongoTemplate.find(billingQuery, BillingInfo.class);
				
				// 统计所有门诊账单的总金额合计
				int invoiceTotalAmount = 0;
				// 统计所有门诊账单的数量
				int invoiceCount = billingInfos.size();
				// 统计所有门诊账单的不可报销金额合计
				int nonReimbursableAmount = 0;
				
				for (BillingInfo billingInfo : billingInfos) {
					if (billingInfo.getAmountInCent() != null) {
						invoiceTotalAmount += billingInfo.getAmountInCent();
					}
					
					// 查询当前账单的所有明细
					Query detailQuery = new Query();
					detailQuery.addCriteria(Criteria.where("billingInfoId").is(billingInfo.getId()));
					List<BillingDetail> billingDetails = mongoTemplate.find(detailQuery, BillingDetail.class);
					
					// 统计当前账单的所有明细的不可报销金额合计
					for (BillingDetail billingDetail : billingDetails) {
						if (billingDetail.getNonReimbursableAmountInCent() != null) {
							nonReimbursableAmount += billingDetail.getNonReimbursableAmountInCent();
						}
					}
				}
				
				// 将统计的金额赋值给发票总金额字段
				claimsInformation.getClaimsData().setInvoiceTotalAmountInCent(invoiceTotalAmount);
				
				// 将统计的数量赋值给发票张数字段
				claimsInformation.getClaimsData().setInvoiceCount(invoiceCount);
				
				// 将统计的金额赋值给不可报销金额字段
				claimsInformation.getClaimsData().setNonReimbursableAmountInCent(nonReimbursableAmount);
				
				// 统计所有门诊账单的账单分组合理费用总额
				int totalReasonableFee = 0;
				for (BillingInfo billingInfo : billingInfos) {
					if (billingInfo.getTotalReasonableFeeInCent() != null) {
						totalReasonableFee += billingInfo.getTotalReasonableFeeInCent();
					}
				}
				
				// 将统计的合理费用总额赋值给可报销金额字段
				claimsInformation.getClaimsData().setReimbursableAmountInCent(totalReasonableFee);
				
				// 计算应付总金额 = 可报销金额 + 住院伙食补助金额
				int totalPayableAmount = totalReasonableFee;
				if (claimsInformation.getClaimsData().getFoodAllowanceAmountInCent() != null) {
					totalPayableAmount += claimsInformation.getClaimsData().getFoodAllowanceAmountInCent();
				}
				claimsInformation.getClaimsData().setTotalPayableAmountInCent(totalPayableAmount);
				
				// 核算明细（门诊），统计所有门诊账单的billingDetailsGroup计算赋值到OutpatientClearing
				if (claimsInformation.getOutpatientClearing() != null) {
					OutpatientClearing outpatientClearing = claimsInformation.getOutpatientClearing();
					int totalClaimAmount = 0;
					int totalDeductionAmount = 0;
					int totalOutpatientPayableAmount = 0;
					
					// 清空原有的核销项目列表
					if (outpatientClearing.getClearingItems() == null) {
						outpatientClearing.setClearingItems(new ArrayList<>());
					} else {
						outpatientClearing.getClearingItems().clear();
					}
					
					// 遍历所有门诊账单
					for (BillingInfo billingInfo : billingInfos) {
						if (billingInfo.getBillingDetailsGroup() != null && !billingInfo.getBillingDetailsGroup().isEmpty()) {
							for (BillingDetailsGroup group : billingInfo.getBillingDetailsGroup()) {
								// 创建核销项目
								OutpatientClearingItem item = new OutpatientClearingItem();
								item.setExpenseItem(group.getFeeType());
								item.setTotalClaimAmountInCent(group.getBillAmountInCent() != null ? group.getBillAmountInCent() : 0);
								
								// 计算总扣减费用 = 审核扣减 + 非工伤扣减
								int totalDeduction = 0;
								if (group.getAuditDeductionInCent() != null) {
									totalDeduction += group.getAuditDeductionInCent();
								}
								if (group.getNonWorkInjuryDeductionInCent() != null) {
									totalDeduction += group.getNonWorkInjuryDeductionInCent();
								}
								item.setTotalDeductionAmountInCent(totalDeduction);
								
								// 计算可支付金额
								int payableAmount = item.getTotalClaimAmountInCent() - item.getTotalDeductionAmountInCent();
								item.setPayableAmountInCent(payableAmount);
								
								// 累加到总计
								totalClaimAmount += item.getTotalClaimAmountInCent();
								totalDeductionAmount += item.getTotalDeductionAmountInCent();
								totalOutpatientPayableAmount += payableAmount;
								
								// 添加到核销项目列表
								outpatientClearing.getClearingItems().add(item);
							}
						}
					}
					
					// 设置总计字段
					outpatientClearing.setTotalClaimAmountInCent(totalClaimAmount);
					outpatientClearing.setTotalDeductionAmountInCent(totalDeductionAmount);
					outpatientClearing.setTotalPayableAmountInCent(totalOutpatientPayableAmount);
				}
			}
			
			// 计算理算结果中的各个字段
			if (claimsInformation.getClaimsResult() != null) {
				ClaimsResult claimsResult = claimsInformation.getClaimsResult();
				
				// 获取当前案件的理算信息Map
				Map<String, MedicalCases.ClaimsInformation> allClaimsInformations = medicalCases.getClaimsInformations();
				
				// 初始化总计字段
				int totalClaimAmount = 0;      // 申报总金额
				int totalNonReimbursable = 0;  // 不可报销金额
				int totalPayable = 0;          // 应付总金额
				int totalThirdPartyPay = 0;    // 第三方赔付金额
				int totalThirdPartyActualPay = 0; // 第三方实际赔付金额
				int totalActualPay = 0;        // 实际支付金额
				
				// 首先计算当前理算条的数据
				if (claimsInformation.getClaimsData() != null) {
					ClaimsData claimsData = claimsInformation.getClaimsData();
					
					// 累计申报总金额 = 发票总额 + 住院伙食补助金额
					if (claimsData.getInvoiceTotalAmountInCent() != null) {
						totalClaimAmount += claimsData.getInvoiceTotalAmountInCent();
					}
					if (claimsData.getFoodAllowanceAmountInCent() != null) {
						totalClaimAmount += claimsData.getFoodAllowanceAmountInCent();
					}
					
					// 累计不可报销金额
					if (claimsData.getNonReimbursableAmountInCent() != null) {
						totalNonReimbursable += claimsData.getNonReimbursableAmountInCent();
					}
					
					// 累计应付总金额
					if (claimsData.getTotalPayableAmountInCent() != null) {
						totalPayable += claimsData.getTotalPayableAmountInCent();
					}
				}
				
				// 如果理算信息Map存在，则统计HOSPITALIZATION_COST和OUTPATIENT_COST的数据总和
				if (allClaimsInformations != null && !allClaimsInformations.isEmpty()) {
					// 遍历所有理算条
					for (Map.Entry<String, MedicalCases.ClaimsInformation> entry : allClaimsInformations.entrySet()) {
						String responsibilityCode = entry.getKey();
						MedicalCases.ClaimsInformation info = entry.getValue();
						
						// 只统计HOSPITALIZATION_COST和OUTPATIENT_COST的理算条
						if (("HOSPITALIZATION_COST".equals(responsibilityCode) || "OUTPATIENT_COST".equals(responsibilityCode))
							&& !responsibilityCode.equals(code)) {  // 排除当前理算条，因为已经计算过
							if (info.getClaimsData() != null) {
								ClaimsData claimsData = info.getClaimsData();
								
								// 累计申报总金额 = 发票总额 + 住院伙食补助金额
								if (claimsData.getInvoiceTotalAmountInCent() != null) {
									totalClaimAmount += claimsData.getInvoiceTotalAmountInCent();
								}
								if (claimsData.getFoodAllowanceAmountInCent() != null) {
									totalClaimAmount += claimsData.getFoodAllowanceAmountInCent();
								}
								
								// 累计不可报销金额
								if (claimsData.getNonReimbursableAmountInCent() != null) {
									totalNonReimbursable += claimsData.getNonReimbursableAmountInCent();
								}
								
								// 累计应付总金额
								if (claimsData.getTotalPayableAmountInCent() != null) {
									totalPayable += claimsData.getTotalPayableAmountInCent();
								}
							}
							
							if (info.getClaimsResult() != null) {
								ClaimsResult result = info.getClaimsResult();
								
								// 累计第三方赔付金额
								if (result.getThirdPartyPayAmountInCent() != null) {
									totalThirdPartyPay += result.getThirdPartyPayAmountInCent();
								}
								
								// 累计第三方实际赔付金额
								if (result.getThirdPartyActualPayAmountInCent() != null) {
									totalThirdPartyActualPay += result.getThirdPartyActualPayAmountInCent();
								}
								
								// 累计实际支付金额
								if (result.getActualPayAmountInCent() != null) {
									totalActualPay += result.getActualPayAmountInCent();
								}
							}
						}
					}
				}
				
				// 设置理算结果的总计字段
				claimsResult.setTotalClaimAmountInCent(totalClaimAmount);
				claimsResult.setNonReimbursableAmountInCent(totalNonReimbursable);
				claimsResult.setTotalPayableAmountInCent(totalPayable);
				claimsResult.setThirdPartyPayAmountInCent(totalThirdPartyPay);
				
				// 计算第三方实际赔付金额 = 第三方赔付金额 * 报销比例
				if (claimsResult.getThirdPartyPayAmountInCent() != null && claimsResult.getReimbursementRatio() != null) {
					int thirdPartyActualPayAmount = (int) (claimsResult.getThirdPartyPayAmountInCent() * claimsResult.getReimbursementRatio().doubleValue());
					claimsResult.setThirdPartyActualPayAmountInCent(thirdPartyActualPayAmount);
				}
				
				// 计算实际支付金额 = 申报总金额 - 不可报销金额 - 第三方实际赔付金额
				int actualPayAmount = claimsResult.getTotalClaimAmountInCent() -
									(claimsResult.getNonReimbursableAmountInCent() != null ? claimsResult.getNonReimbursableAmountInCent() : 0) -
									(claimsResult.getThirdPartyActualPayAmountInCent() != null ? claimsResult.getThirdPartyActualPayAmountInCent() : 0);
				claimsResult.setActualPayAmountInCent(actualPayAmount);
			}
			
			// 更新数据库中的理算信息
			mongoTemplate.save(medicalCases);
		}

		return CommonResult.successData(claimsInformation);
	}

	/**
	 * 创建默认的理算条信息
	 * 当code为OUTPATIENT_COST时，封装OutpatientClearing
	 * 当code为HOSPITALIZATION_COST时，封装HospitalClearing
	 *
	 * @param code 责任树编码
	 * @return 默认的理算条信息
	 */
	private MedicalCases.ClaimsInformation createDefaultClaimsInformation(String code) {
		MedicalCases.ClaimsInformation claimsInformation = new MedicalCases.ClaimsInformation();
		claimsInformation.setResponsibilityTreeCode(code);
		claimsInformation.setClaimsStatus(false); // 未理算

		// 判断是门诊还是住院
		boolean isOutpatient = "OUTPATIENT_COST".equals(code);
		boolean isHospitalization = "HOSPITALIZATION_COST".equals(code);

		// 创建理算数据
		ClaimsData claimsData = new ClaimsData();
		claimsData.setHistoryPayCount(0);
		claimsData.setHistoryPayDays(0);
		claimsData.setHistoryPayAmountInCent(0);
		claimsData.setIsHospitalFoodAllowance(isHospitalization); // 选择住院责任条的自动选择是；否则自动选择否
		claimsData.setFoodAllowanceStandardInCent(0);
		claimsData.setTotalPayDays(0);
		claimsData.setFoodAllowanceAmountInCent(0);
		claimsData.setInvoiceTotalAmountInCent(0);
		claimsData.setInvoiceCount(0);
		claimsData.setNonReimbursableAmountInCent(0);
		claimsData.setReimbursableAmountInCent(0);
		claimsData.setTotalPayableAmountInCent(0);

		// 根据责任树编码创建相应的核销明细
		if (isOutpatient) {
			// 创建门诊核销明细
			OutpatientClearing outpatientClearing = new OutpatientClearing();
			outpatientClearing.setClearingItems(new ArrayList<>());
			outpatientClearing.setTotalClaimAmountInCent(0);
			outpatientClearing.setTotalDeductionAmountInCent(0);
			outpatientClearing.setTotalPayableAmountInCent(0);

			// 设置门诊核销明细
			claimsData.setOutpatientClearing(outpatientClearing);
			claimsInformation.setOutpatientClearing(outpatientClearing);
		} else if (isHospitalization) {
			// 创建住院核销明细
			HospitalClearing hospitalClearing = new HospitalClearing();
			hospitalClearing.setClearingItems(new ArrayList<>());
			hospitalClearing.setTotalInvoiceAmountInCent(0);
			hospitalClearing.setTotalReimbursableAmountInCent(0);
			hospitalClearing.setTotalNonReimbursableAmountInCent(0);
			hospitalClearing.setTotalHospitalDays(0);
			hospitalClearing.setTotalHospitalFoodAllowanceInCent(0);
			hospitalClearing.setTotalPayableAmountInCent(0);

			// 设置住院核销明细
			claimsData.setHospitalClearing(hospitalClearing);
			claimsInformation.setHospitalClearing(hospitalClearing);
		} else {
			// 对于其他类型的责任树编码，可以根据需要设置相应的默认值
			// 这里可以不设置任何核销明细，或者设置空的核销明细
		}

		// 设置ClaimsData
		claimsInformation.setClaimsData(claimsData);

		// 创建理算结果
		ClaimsResult claimsResult = new ClaimsResult();
		claimsResult.setTotalClaimAmountInCent(0);
		claimsResult.setNonReimbursableAmountInCent(0);
		claimsResult.setTotalPayableAmountInCent(0);
		claimsResult.setThirdPartyPayAmountInCent(0);
		claimsResult.setReimbursementRatio(new java.math.BigDecimal("1.0")); // 默认报销比例为100%
		claimsResult.setThirdPartyActualPayAmountInCent(0);
		claimsResult.setActualPayAmountInCent(0);
		claimsResult.setRemarks("");

		// 设置ClaimsResult
		claimsInformation.setClaimsResult(claimsResult);

		return claimsInformation;
	}

	/**
	 * 历史案件查询接口
	 * 支持按姓名、身份证、单位名称、状态等多条件查询历史案件
	 *
	 * @param workerName      职工姓名（可选）
	 * @param idCard          身份证号（可选）
	 * @param organization    单位名称（可选）
	 * @param acceptDateStart 受理开始时间（可选）
	 * @param acceptDateEnd   受理结束时间（可选）
	 * @param statusList      案件状态列表（可选）
	 * @param commonPage      分页信息
	 * @return 历史案件列表（分页）
	 */
	@GetMapping("/history-cases")
	public CommonResult<List<MedicalCases>> getHistoryCases(
			@RequestParam(required = false) String workerName,
			@RequestParam(required = false) String idCard,
			@RequestParam(required = false) String organization,
			@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateStart,
			@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date acceptDateEnd,
			@RequestParam(required = false) List<MedicalCasesStatus> statusList,
			CommonPage commonPage) {

		Query query = new Query();

		// 添加查询条件
		if (StrUtil.isNotBlank(workerName)) {
			query.addCriteria(Criteria.where("workerName").regex(".*" + workerName + ".*", "i"));
		}

		if (StrUtil.isNotBlank(idCard)) {
			query.addCriteria(Criteria.where("idCard").regex(".*" + idCard + ".*", "i"));
		}

		if (StrUtil.isNotBlank(organization)) {
			query.addCriteria(Criteria.where("organization").regex(".*" + organization + ".*", "i"));
		}

		// 受理时间范围查询
		if (acceptDateStart != null || acceptDateEnd != null) {
			Criteria dateCriteria = Criteria.where("acceptDate");
			if (acceptDateStart != null && acceptDateEnd != null) {
				dateCriteria.gte(acceptDateStart).lte(acceptDateEnd);
			} else if (acceptDateStart != null) {
				dateCriteria.gte(acceptDateStart);
			} else if (acceptDateEnd != null) {
				dateCriteria.lte(acceptDateEnd);
			}
			query.addCriteria(dateCriteria);
		}

		// 状态查询
		if (statusList != null && !statusList.isEmpty()) {
			query.addCriteria(Criteria.where("status").in(statusList));
		}

		// 按创建时间倒序排序
		query.with(Sort.by(Sort.Direction.DESC, "createTime"));

		// 设置分页信息
		com.yixun.wid.utils.MongoUtil.setPageInfo(mongoTemplate, MedicalCases.class, query, commonPage);

		// 执行查询
		List<MedicalCases> list = mongoTemplate.find(query, MedicalCases.class);

		// 加载关联的账单信息
		for (MedicalCases medicalCases : list) {
			loadBillingInfos(medicalCases);
		}

		return CommonResult.successPageData(list, commonPage);
	}

	/**
	 * 账单重复报销查询接口
	 * 基于账单号查询历史已办结案件的账单号是否存在重复
	 *
	 * @param request 账单重复查询请求，包含账单号列表
	 * @return 重复查询结果，若存在重复则返回对应的账单信息
	 */
	@PostMapping("/bill-duplicate-check")
	public CommonResult<BillDuplicateCheckResponse> checkBillDuplicate(@RequestBody BillDuplicateCheckRequest request) {
		if (request.getBillIds() == null || request.getBillIds().isEmpty()) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "账单号列表不能为空");
		}

		BillDuplicateCheckResponse response = new BillDuplicateCheckResponse();
		response.setHasDuplicate(false);
		response.setDuplicateBills(new ArrayList<>());

		// 查询已办结案件的账单信息
		Query billingQuery = new Query();
		billingQuery.addCriteria(Criteria.where("billId").in(request.getBillIds()));
		List<BillingInfo> duplicateBillingInfos = mongoTemplate.find(billingQuery, BillingInfo.class);

		if (!duplicateBillingInfos.isEmpty()) {
			// 获取这些账单对应的案件ID
			List<Long> medicalCasesIds = duplicateBillingInfos.stream()
				.map(BillingInfo::getMedicalCasesId)
				.distinct()
				.collect(Collectors.toList());

			// 查询这些案件，只查已办结的
			Query casesQuery = new Query();
			casesQuery.addCriteria(Criteria.where("id").in(medicalCasesIds));
			casesQuery.addCriteria(Criteria.where("status").is(MedicalCasesStatus.Done));
			List<MedicalCases> doneCases = mongoTemplate.find(casesQuery, MedicalCases.class);

			if (!doneCases.isEmpty()) {
				// 创建案件ID到案件对象的映射
				Set<Long> doneCaseIds = doneCases.stream()
					.map(MedicalCases::getId)
					.collect(Collectors.toSet());

				// 筛选出属于已办结案件的账单
				List<BillingInfo> duplicateBills = duplicateBillingInfos.stream()
					.filter(billingInfo -> doneCaseIds.contains(billingInfo.getMedicalCasesId()))
					.collect(Collectors.toList());

				if (!duplicateBills.isEmpty()) {
					response.setHasDuplicate(true);
					response.setDuplicateBills(duplicateBills);
				}
			}
		}

		return CommonResult.successData(response);
	}

	private MedicalCasesStatus getNextStatus(MedicalCasesStatus current) {
		if (current == MedicalCasesStatus.Applying) {
			return MedicalCasesStatus.PreReviewing;
		}
		if (current == MedicalCasesStatus.PreReviewing) {
			return MedicalCasesStatus.Reviewing;
		}
		if (current == MedicalCasesStatus.Reviewing) {
			return MedicalCasesStatus.FinalReviewing;
		}
		if (current == MedicalCasesStatus.FinalReviewing) {
			return MedicalCasesStatus.Done;
		}
		return current;
	}

	/**
	 * 受理信息和临床诊断信息识别接口
	 * 支持两种调用方式：
	 * 1. 传入MedicalCases的ID，系统自动查询对应的AcceptedInformationDiagnosisRequest
	 * 2. 直接传入AcceptedInformationDiagnosisRequest对象
	 *
	 * 注意：此接口仅返回AI识别结果，不会自动保存到数据库，是否保存由用户决定
	 *
	 * @param id MedicalCases的ID（与request参数二选一）
	 * @param request AcceptedInformationDiagnosisRequest对象（与id参数二选一）
	 * @return 包含AI识别的受理信息和临床诊断数据的VO对象（仅用于展示，未保存到数据库）
	 */
	@PostMapping("/accepted-information-diagnosis")
	public CommonResult<AcceptedInformationDiagnosisVO> acceptedInformationDiagnosis(
			@RequestParam(required = false) Long id,
			@RequestBody(required = false) AcceptedInformationDiagnosisRequest request) {

		// 参数验证：id和request必须有一个不为空
		if (id == null && request == null) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "参数错误：id和request参数必须提供其中一个");
		}

		if (id != null && request != null) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "参数错误：id和request参数只能提供其中一个");
		}

		// 如果传入的是ID，则查询对应的AcceptedInformationDiagnosisRequest
		if (id != null) {
			MedicalCases existingCase = mongoTemplate.findById(id, MedicalCases.class);
			if (existingCase == null) {
				return CommonResult.failResult(CommonErrorInfo.code_6001, "工伤待遇业务不存在");
			}

			request = existingCase.getAcceptedInformationDiagnosisRequest();
			if (request == null) {
				return CommonResult.failResult(CommonErrorInfo.code_1001, "该案件尚未进行材料分类，请先进行材料分类");
			}

			log.info("开始受理信息和临床诊断识别，案件ID: {}", id);
		} else {
			log.info("开始受理信息和临床诊断识别，直接传入request对象");
		}

		// 调用AI工具进行受理信息和临床诊断识别
		AcceptedInformationDiagnosisResponse response = aiUtils.acceptedInformationDiagnosis(request);

		// 检查AI调用状态
		if (response == null) {
			String errorMsg = "受理信息和临床诊断识别失败：AI服务返回空响应";
			log.error(errorMsg);
			throw new DataErrorException(errorMsg);
		}

		if (StrUtil.isBlank(response.getStatus()) || !"success".equals(response.getStatus())) {
			String errorMsg = String.format("受理信息和临床诊断识别失败：status=%s, message=%s",
				response.getStatus(), response.getMessage());
			log.error(errorMsg);
			throw new DataErrorException(errorMsg);
		}

		// 创建一个新的AcceptedInformationDiagnosisVO对象，将AI识别结果映射到其中
		AcceptedInformationDiagnosisVO resultVO = new AcceptedInformationDiagnosisVO();

		// 如果是通过ID调用，保留原有的案件ID
		if (id != null) {
			resultVO.setId(id);
		}

		// 将AI识别结果映射到VO对象
		mapDiagnosisDataToVO(resultVO, response);

		log.info("受理信息和临床诊断识别完成，识别状态: {}", response.getStatus());

		return CommonResult.successData(resultVO);
	}

	/**
	 * 更新待遇材料和材料类型识别响应数据
	 * 根据MedicalCases的ID更新对应的待遇材料和材料类型识别响应数据
	 *
	 * @param request 更新请求参数，包含id和需要更新的数据
	 * @return 更新结果
	 */
	@PostMapping("/update-materials")
	public CommonResult<Void> updateMaterials(@RequestBody UpdateMaterialsRequest request) {

		// 验证请求参数
		if (request == null || request.getId() == null) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "请求参数不能为空，必须提供案件ID");
		}

		Long id = request.getId();

		// 根据ID查询MedicalCases
		MedicalCases medicalCases = mongoTemplate.findById(id, MedicalCases.class);
		if (medicalCases == null) {
			return CommonResult.failResult(CommonErrorInfo.code_6001, "工伤待遇业务不存在");
		}

		boolean hasUpdate = false;

		// 更新待遇材料
		if (request.getTreatmentMaterials() != null) {
			medicalCases.setTreatmentMaterials(request.getTreatmentMaterials());
			hasUpdate = true;
			log.info("更新待遇材料，案件ID: {}, 材料数量: {}", id, request.getTreatmentMaterials().size());
		}

		// 更新材料类型识别响应数据
		if (request.getAcceptedInformationDiagnosisRequest() != null) {
			medicalCases.setAcceptedInformationDiagnosisRequest(request.getAcceptedInformationDiagnosisRequest());
			hasUpdate = true;
			log.info("更新材料类型识别响应数据，案件ID: {}", id);
		}

		if (!hasUpdate) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "没有提供需要更新的数据");
		}

		// 设置更新时间和操作用户信息
		Date now = new Date();
		medicalCases.setUpdateTime(now);

		Long recentUserId = ServletRequestUtils.getUserId();
		String recentUserName = getUserName();
		medicalCases.setRecentUserId(String.valueOf(recentUserId));
		medicalCases.setRecentUserName(recentUserName);

		// 保存更新后的数据
		mongoTemplate.save(medicalCases);

		// 记录操作日志
		MedicalCasesLog logEntry = new MedicalCasesLog();
		logEntry.setMedicalCasesId(medicalCases.getId());
		logEntry.setStatus("材料数据更新");
		logEntry.setUserId(String.valueOf(recentUserId));
		logEntry.setUserName(recentUserName);
		logEntry.setCreateTime(now);
		medicalCasesLogService.add(logEntry);

		log.info("材料数据更新完成，案件ID: {}", id);

		return CommonResult.successResult("材料数据更新成功");
	}

	/**
	 * 手术信息识别接口
	 * 支持两种调用方式：
	 * 1. 传入MedicalCases的ID，系统自动查询对应的AcceptedInformationDiagnosisRequest
	 * 2. 直接传入AcceptedInformationDiagnosisRequest对象
	 *
	 * 注意：此接口仅返回AI识别结果，不会自动保存到数据库，是否保存由用户决定
	 *
	 * @param id MedicalCases的ID（与request参数二选一）
	 * @param request AcceptedInformationDiagnosisRequest对象（与id参数二选一）
	 * @return 包含AI识别的手术信息数据的VO对象（仅用于展示，未保存到数据库）
	 */
	@PostMapping("/surgical-information")
	public CommonResult<SurgicalInformationVO> surgicalInformation(
			@RequestParam(required = false) Long id,
			@RequestBody(required = false) AcceptedInformationDiagnosisRequest request) {

		// 参数验证：id和request必须有一个不为空
		if (id == null && request == null) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "参数错误：id和request参数必须提供其中一个");
		}

		if (id != null && request != null) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "参数错误：id和request参数只能提供其中一个");
		}

		// 如果传入的是ID，则查询对应的AcceptedInformationDiagnosisRequest
		if (id != null) {
			MedicalCases medicalCases = mongoTemplate.findById(id, MedicalCases.class);
			if (medicalCases == null) {
				return CommonResult.failResult(CommonErrorInfo.code_6001, "工伤待遇业务不存在");
			}

			request = medicalCases.getAcceptedInformationDiagnosisRequest();
			if (request == null) {
				return CommonResult.failResult(CommonErrorInfo.code_1001, "该案件尚未进行材料分类，请先进行材料分类");
			}

			log.info("开始手术信息识别，案件ID: {}", id);
		} else {
			log.info("开始手术信息识别，直接传入request对象");
		}

		// 调用AI工具进行手术信息识别
		SurgicalInformationResponse response = aiUtils.surgicalInformation(request);

		// 检查AI识别结果状态
		if (StrUtil.isBlank(response.getStatus()) || !"success".equals(response.getStatus())) {
			String errorMsg = String.format("手术信息识别失败：status=%s, message=%s",
				response.getStatus(), response.getMessage());
			log.error(errorMsg);
			throw new DataErrorException(errorMsg);
		}

		// 创建一个新的SurgicalInformationVO对象，将AI识别结果映射到其中
		SurgicalInformationVO resultVO = new SurgicalInformationVO();

		// 如果是通过ID调用，保留原有的案件ID
		if (id != null) {
			resultVO.setId(id);
		}

		// 将AI识别结果映射到VO对象
		mapSurgicalDataToVO(resultVO, response);

		if (id != null) {
			log.info("手术信息识别完成，案件ID: {}, 识别状态: {}", id, response.getStatus());
		} else {
			log.info("手术信息识别完成，识别状态: {}", response.getStatus());
		}

		return CommonResult.successData(resultVO);
	}

	/**
	 * 账单信息识别接口
	 * 支持两种调用方式：
	 * 1. 传入MedicalCases的ID，系统自动查询对应的AcceptedInformationDiagnosisRequest
	 * 2. 直接传入AcceptedInformationDiagnosisRequest对象
	 *
	 * 注意：此接口仅返回AI识别结果，不会自动保存到数据库，是否保存由用户决定
	 *
	 * @param id MedicalCases的ID（与request参数二选一）
	 * @param request AcceptedInformationDiagnosisRequest对象（与id参数二选一）
	 * @return 包含AI识别的账单信息数据的VO对象（仅用于展示，未保存到数据库）
	 */
	@PostMapping("/bill-information")
	public CommonResult<BillInformationVO> billInformation(
			@RequestParam(required = false) Long id,
			@RequestBody(required = false) AcceptedInformationDiagnosisRequest request) {

		// 参数验证：id和request必须有一个不为空
		if (id == null && request == null) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "参数错误：id和request参数必须提供其中一个");
		}

		if (id != null && request != null) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "参数错误：id和request参数只能提供其中一个");
		}

		// 如果传入的是ID，则查询对应的AcceptedInformationDiagnosisRequest
		if (id != null) {
			MedicalCases medicalCases = mongoTemplate.findById(id, MedicalCases.class);
			if (medicalCases == null) {
				return CommonResult.failResult(CommonErrorInfo.code_6001, "工伤待遇业务不存在");
			}

			request = medicalCases.getAcceptedInformationDiagnosisRequest();
			if (request == null) {
				return CommonResult.failResult(CommonErrorInfo.code_1001, "该案件尚未进行材料分类，请先进行材料分类");
			}

			log.info("开始账单信息识别，案件ID: {}", id);
		} else {
			log.info("开始账单信息识别，直接传入request对象");
		}

		// 调用AI工具进行账单信息识别
		BillInformationResponse response = aiUtils.billInformation(request);

		// 检查AI识别结果状态
		if (StrUtil.isBlank(response.getStatus()) || !"success".equals(response.getStatus())) {
			String errorMsg = String.format("账单信息识别失败：status=%s, message=%s",
				response.getStatus(), response.getMessage());
			log.error(errorMsg);
			throw new DataErrorException(errorMsg);
		}

		// 创建一个新的BillInformationVO对象，将AI识别结果映射到其中
		BillInformationVO resultVO = new BillInformationVO();

		// 如果是通过ID调用，保留原有的案件ID
		if (id != null) {
			resultVO.setId(id);
		}

		// 将AI识别结果映射到VO对象
		mapBillDataToVO(resultVO, response);

		if (id != null) {
			log.info("账单信息识别完成，案件ID: {}, 识别状态: {}", id, response.getStatus());
		} else {
			log.info("账单信息识别完成，识别状态: {}", response.getStatus());
		}

		return CommonResult.successData(resultVO);
	}

	/**
	 * 账单OCR接口
	 * 直接对单个账单文件进行OCR识别，无需依赖材料分类结果
	 *
	 * 注意：此接口仅返回AI识别结果，不会自动保存到数据库，是否保存由用户决定
	 *
	 * @param request 账单OCR请求参数
	 * @return 包含AI识别的账单信息数据的VO对象（仅用于展示，未保存到数据库）
	 */
	@PostMapping("/bill-ocr")
	public CommonResult<BillInformationVO> billOcr(@RequestBody BillOcrRequest request) {
		// 参数验证
		if (request == null || StrUtil.isBlank(request.getFile())) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "文件URL不能为空");
		}

		String fileUrl = request.getFile();
		log.info("开始账单OCR识别，文件URL: {}", fileUrl);

		try {
			// 调用AI工具进行账单OCR识别
			BillInformationResponse response = aiUtils.billOcr(fileUrl);

			// 检查AI识别结果状态
			if (StrUtil.isBlank(response.getStatus()) || !"success".equals(response.getStatus())) {
				String errorMsg = String.format("账单OCR识别失败：status=%s, message=%s",
					response.getStatus(), response.getMessage());
				log.error(errorMsg);
				throw new DataErrorException(errorMsg);
			}

			// 创建一个新的BillInformationVO对象，将AI识别结果映射到其中
			BillInformationVO resultVO = new BillInformationVO();
			resultVO.setFileUrl(fileUrl);

			// 将AI识别结果映射到VO对象
			mapBillDataToVO(resultVO, response);

			log.info("账单OCR识别完成，文件URL: {}, 识别状态: {}", fileUrl, response.getStatus());

			return CommonResult.successData(resultVO);
		} catch (Exception e) {
			log.error("账单OCR识别失败，文件URL: {}", fileUrl, e);
			return CommonResult.failResult(CommonErrorInfo.code_1001, "账单OCR识别失败: " + e.getMessage());
		}
	}

	/**
	 * 清单OCR接口
	 * 直接对单个清单文件进行OCR识别
	 *
	 * 注意：此接口仅返回AI识别结果，不会自动保存到数据库，是否保存由用户决定
	 *
	 * @param request 清单OCR请求参数
	 * @return 包含AI识别的清单信息数据的VO对象（仅用于展示，未保存到数据库）
	 */
	@PostMapping("/list-ocr")
	public CommonResult<ListOcrVO> listOcr(@RequestBody ListOcrRequest request) {
		// 参数验证
		if (request == null || StrUtil.isBlank(request.getFile())) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "文件URL不能为空");
		}

		String fileUrl = request.getFile();
		log.info("开始清单OCR识别，文件URL: {}", fileUrl);

		try {
			// 调用AI工具进行清单OCR识别
			ListOcrResponse response = aiUtils.listOcr(fileUrl);

			// 检查AI识别结果状态
			if (StrUtil.isBlank(response.getStatus()) || !"success".equals(response.getStatus())) {
				String errorMsg = String.format("清单OCR识别失败：status=%s, message=%s",
					response.getStatus(), response.getMessage());
				log.error(errorMsg);
				throw new DataErrorException(errorMsg);
			}

			// 创建一个新的ListOcrVO对象，将AI识别结果映射到其中
			ListOcrVO resultVO = new ListOcrVO();
			resultVO.setFileUrl(fileUrl);

			// 将AI识别结果映射到VO对象
			mapListOcrDataToVO(resultVO, response);

			log.info("清单OCR识别完成，文件URL: {}, 识别状态: {}", fileUrl, response.getStatus());

			return CommonResult.successData(resultVO);
		} catch (Exception e) {
			log.error("清单OCR识别失败，文件URL: {}", fileUrl, e);
			return CommonResult.failResult(CommonErrorInfo.code_1001, "清单OCR识别失败: " + e.getMessage());
		}
	}

	/**
	 * 计算器接口
	 * 提供数学计算功能，支持基本的数学运算
	 *
	 * @param request 计算器请求参数
	 * @return 计算结果
	 */
	@PostMapping("/calculator")
	public CommonResult<CalculatorResponse> calculator(@RequestBody CalculatorRequest request) {
		// 参数验证
		if (request == null || StrUtil.isBlank(request.getInput())) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "计算表达式不能为空");
		}

		String input = request.getInput();
		log.info("开始计算器运算，表达式: {}", input);

		try {
			// 调用AI工具进行计算
			CalculatorResponse response = aiUtils.calculator(input);

			log.info("计算器运算完成，表达式: {}, 计算状态: {}", input, response.getStatus());

			return CommonResult.successData(response);
		} catch (Exception e) {
			log.error("计算器运算失败，表达式: {}", input, e);
			return CommonResult.failResult(CommonErrorInfo.code_1001, "计算器运算失败: " + e.getMessage());
		}
	}

	/**
	 * 诊疗项目AI查询 项目查询接口
	 * 提供医疗项目查询功能，支持智能搜索和匹配，同时支持项目费用相似搜索
	 *
	 * @param request 项目查询请求参数
	 * @return 项目查询结果
	 */
	@PostMapping("/project-search")
	public CommonResult<ProjectSearchSimpleResponse> projectSearch(@RequestBody ProjectSearchRequest request) {
		// 参数验证
		if (request == null || StrUtil.isBlank(request.getProjectName())) {
			return CommonResult.failResult(CommonErrorInfo.code_1001, "项目名称不能为空");
		}

		String projectName = request.getProjectName();
		log.info("开始项目查询，项目名称: {}, 启用项目费用搜索: {}", projectName, request.getEnableProjectFeeSearch());

		try {
			// 调用AI工具进行项目查询
			ProjectSearchResponse response = aiUtils.projectSearch(projectName);

			// 创建简化响应对象
			ProjectSearchSimpleResponse simpleResponse = new ProjectSearchSimpleResponse();

			// 设置前端查询结果
			if (response.getData() != null) {
				simpleResponse.setResult(response.getData().getResult());
			}

			// 如果启用了项目费用相似搜索
			if (Boolean.TRUE.equals(request.getEnableProjectFeeSearch())) {
				log.info("开始项目费用相似搜索，项目名称: {}", projectName);

				// 构建项目费用搜索参数
				ProjectFeeSimilarSearchIn projectFeeSearchIn = new ProjectFeeSimilarSearchIn();
				projectFeeSearchIn.setProjectName(projectName);
				projectFeeSearchIn.setHospitalId(request.getHospitalId());
				projectFeeSearchIn.setHospitalName(request.getHospitalName());
				projectFeeSearchIn.setHospitalAlias(request.getHospitalAlias());
				projectFeeSearchIn.setTopK(request.getTopK());
				projectFeeSearchIn.setSimilarityThreshold(request.getSimilarityThreshold());

				try {
					// 调用项目费用相似搜索
					ProjectFee projectFee = projectFeeService.similarSearch(projectFeeSearchIn);
					simpleResponse.setProjectFee(projectFee);

					if (projectFee != null) {
						log.info("项目费用相似搜索成功，找到项目: {}", projectFee.getProjectName());
					} else {
						log.info("项目费用相似搜索完成，未找到匹配项目");
					}
				} catch (Exception e) {
					log.error("项目费用相似搜索异常", e);
					// 搜索失败时projectFee保持为null
				}
			}

			log.info("项目查询完成，项目名称: {}", projectName);

			return CommonResult.successData(simpleResponse);
		} catch (Exception e) {
			log.error("项目查询失败，项目名称: {}", projectName, e);
			return CommonResult.failResult(CommonErrorInfo.code_1001, "项目查询失败: " + e.getMessage());
		}
	}

	/**
	 * 将AcceptedInformationDiagnosisResponse的数据映射到AcceptedInformationDiagnosisVO对象
	 * @param vo 目标VO对象
	 * @param response AI识别响应结果
	 */
	private void mapDiagnosisDataToVO(AcceptedInformationDiagnosisVO vo, AcceptedInformationDiagnosisResponse response) {
		if (response == null || response.getData() == null) {
			return;
		}

		AcceptedInformationDiagnosisResponse.DiagnosisData data = response.getData();

		// 映射事故日期
		if (StrUtil.isNotBlank(data.getAccidentDate())) {
			try {
				SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
				Date accidentDate = dateFormat.parse(data.getAccidentDate());
				vo.setAccidentDate(accidentDate);
			} catch (ParseException e) {
				log.warn("解析事故日期失败: {}", data.getAccidentDate(), e);
			}
		}

		// 映射职工姓名
		if (StrUtil.isNotBlank(data.getEmployeeName())) {
			vo.setWorkerName(data.getEmployeeName());
		}

		// 映射性别
		if (StrUtil.isNotBlank(data.getGender())) {
			vo.setGender(data.getGender());
		}

		// 映射身份证号码
		if (StrUtil.isNotBlank(data.getIdNumber())) {
			vo.setIdCard(data.getIdNumber());
		}

		// 映射用人单位名称
		if (StrUtil.isNotBlank(data.getEmployerName())) {
			vo.setOrganization(data.getEmployerName());
		}

		// 映射临床诊断列表
		if (data.getClinicalDiagnosis() != null && !data.getClinicalDiagnosis().isEmpty()) {
			vo.setInjuryDiagnoses(data.getClinicalDiagnosis());
		}
	}

	/**
	 * 将AcceptedInformationDiagnosisResponse的数据映射到MedicalCases对象
	 * @param medicalCases 目标MedicalCases对象
	 * @param response AI识别响应结果
	 */
	private void mapDiagnosisDataToMedicalCases(MedicalCases medicalCases, AcceptedInformationDiagnosisResponse response) {
		if (response == null || response.getData() == null) {
			return;
		}

		AcceptedInformationDiagnosisResponse.DiagnosisData data = response.getData();

		// 映射事故日期
		if (StrUtil.isNotBlank(data.getAccidentDate())) {
			try {
				SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
				Date accidentDate = dateFormat.parse(data.getAccidentDate());
				medicalCases.setAccidentDate(accidentDate);
			} catch (ParseException e) {
				log.warn("解析事故日期失败: {}", data.getAccidentDate(), e);
			}
		}

		// 映射职工姓名
		if (StrUtil.isNotBlank(data.getEmployeeName())) {
			medicalCases.setWorkerName(data.getEmployeeName());
		}

		// 映射性别
		if (StrUtil.isNotBlank(data.getGender())) {
			medicalCases.setGender(data.getGender());
		}

		// 映射身份证号码
		if (StrUtil.isNotBlank(data.getIdNumber())) {
			medicalCases.setIdCard(data.getIdNumber());
		}

		// 映射用人单位名称
		if (StrUtil.isNotBlank(data.getEmployerName())) {
			medicalCases.setOrganization(data.getEmployerName());
		}

		// 映射临床诊断列表
		if (data.getClinicalDiagnosis() != null && !data.getClinicalDiagnosis().isEmpty()) {
			medicalCases.setInjuryDiagnoses(data.getClinicalDiagnosis());
		}
	}

	/**
	 * 将SurgicalInformationResponse的数据映射到SurgicalInformationVO对象
	 * @param vo 目标VO对象
	 * @param response AI识别响应结果
	 */
	private void mapSurgicalDataToVO(SurgicalInformationVO vo, SurgicalInformationResponse response) {
		if (response == null || response.getData() == null) {
			return;
		}

		SurgicalInformationResponse.SurgicalData data = response.getData();

		// 映射手术名称列表
		if (data.getSurgicalName() != null && !data.getSurgicalName().isEmpty()) {
			vo.setSurgicalNames(data.getSurgicalName());
		}
	}

	/**
	 * 将BillInformationResponse的数据映射到BillInformationVO对象
	 * @param vo 目标VO对象
	 * @param response AI识别响应结果
	 */
	private void mapBillDataToVO(BillInformationVO vo, BillInformationResponse response) {
		if (response == null || response.getData() == null) {
			return;
		}

		BillInformationResponse.BillData data = response.getData();

		// 映射账单信息列表
		if (data.getBillInformation() != null && !data.getBillInformation().isEmpty()) {
			List<BillInformationVO.BillInformation> billInfos = new ArrayList<>();

			for (BillInformationResponse.BillInformation sourceBill : data.getBillInformation()) {
				BillInformationVO.BillInformation targetBill = new BillInformationVO.BillInformation();

				// 映射基本字段，统一为BillingInfo的字段名称
				targetBill.setBillId(sourceBill.getBillNumber()); // billNumber -> billId
				targetBill.setHospital(sourceBill.getHospital());
				targetBill.setTreatmentType(sourceBill.getTreatmentType());

				// 将金额转换为分（如果原来是元）
				if (sourceBill.getBillAmount() != null) {
					targetBill.setAmountInCent(sourceBill.getBillAmount().multiply(new BigDecimal(100)).intValue());
				}

				// 解析时间字段
				try {
					SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
					SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

					// 解析入院时间为就诊日期
					if (StrUtil.isNotBlank(sourceBill.getAdmissionTime())) {
						try {
							targetBill.setVisitDate(dateFormat.parse(sourceBill.getAdmissionTime()));
						} catch (ParseException e) {
							try {
								targetBill.setVisitDate(dateTimeFormat.parse(sourceBill.getAdmissionTime()));
							} catch (ParseException ex) {
								log.warn("解析入院时间失败: {}", sourceBill.getAdmissionTime(), ex);
							}
						}
					}

					// 解析出院时间
					if (StrUtil.isNotBlank(sourceBill.getDischargeTime())) {
						try {
							targetBill.setDischargeDate(dateFormat.parse(sourceBill.getDischargeTime()));
						} catch (ParseException e) {
							try {
								targetBill.setDischargeDate(dateTimeFormat.parse(sourceBill.getDischargeTime()));
							} catch (ParseException ex) {
								log.warn("解析出院时间失败: {}", sourceBill.getDischargeTime(), ex);
							}
						}
					}

					// 解析门诊时间
					if (StrUtil.isNotBlank(sourceBill.getClinicTime())) {
						try {
							targetBill.setOutpatientStartTime(dateFormat.parse(sourceBill.getClinicTime()));
						} catch (ParseException e) {
							try {
								targetBill.setOutpatientStartTime(dateTimeFormat.parse(sourceBill.getClinicTime()));
							} catch (ParseException ex) {
								log.warn("解析门诊时间失败: {}", sourceBill.getClinicTime(), ex);
							}
						}
					}
				} catch (Exception e) {
					log.warn("解析时间字段时发生异常", e);
				}

				// 映射费用明细到账单明细和账单明细分组
				if (sourceBill.getExpenseDetails() != null && !sourceBill.getExpenseDetails().isEmpty()) {
					List<BillingDetail> billingDetails = new ArrayList<>();
					List<BillingDetailsGroup> billingDetailsGroups = new ArrayList<>();

					int orderNum = 1;
					for (BillInformationResponse.ExpenseDetail sourceDetail : sourceBill.getExpenseDetails()) {
						// 创建账单明细分组
						BillingDetailsGroup group = new BillingDetailsGroup();
						group.setFeeType(sourceDetail.getExpenseItem()); // 费用项目作为费用类别
						if (sourceDetail.getAmount() != null) {
							group.setBillAmount(sourceDetail.getAmount()); // 这会自动转换为分
						}
						// 设置金额明细列表
						if (sourceDetail.getAmountList() != null) {
							group.setAmountList(sourceDetail.getAmountList());
						}
						billingDetailsGroups.add(group);

						// 映射费用清单到账单明细
						if (sourceDetail.getExpenseList() != null && !sourceDetail.getExpenseList().isEmpty()) {
							for (BillInformationResponse.ExpenseList sourceList : sourceDetail.getExpenseList()) {
								BillingDetail detail = new BillingDetail();
								detail.setOrderNum(orderNum++);
								detail.setProjectName(sourceList.getName());
								detail.setProjectCode(sourceList.getCode());
								detail.setFeeType(sourceDetail.getExpenseItem()); // 使用费用项目作为费用类别
								detail.setQuantity(sourceList.getQuantity());

								// 设置单价和金额（会自动转换为分）
								if (sourceList.getUnitPrice() != null) {
									detail.setUnitPrice(sourceList.getUnitPrice());
								}
								if (sourceList.getAmount() != null) {
									detail.setAmount(sourceList.getAmount());
								}

								// 设置是否工伤，如果为空默认设置为是
								if (detail.getIsWorkInjury() == null) {
									detail.setIsWorkInjury(true);
								}

								// 基于三目录相似搜索设置费用等级
								threeCatalogueSearchUtils.setFeeLevelAndThreeCatalogueIdBySearch(detail);

								billingDetails.add(detail);
							}
						}
					}

					targetBill.setBillingDetails(billingDetails);
					targetBill.setBillingDetailsGroup(billingDetailsGroups);
				}

				billInfos.add(targetBill);
			}

			vo.setBillInformations(billInfos);
		}
	}

	/**
	 * 将ListOcrResponse的数据映射到ListOcrVO对象
	 * @param vo 目标VO对象
	 * @param response AI识别响应结果
	 */
	private void mapListOcrDataToVO(ListOcrVO vo, ListOcrResponse response) {
		if (response == null || response.getData() == null) {
			return;
		}

		ListOcrResponse.ListOcrData data = response.getData();

		// 映射账单明细
		if (data.getExpenseList() != null && !data.getExpenseList().isEmpty()) {
			List<ListOcrVO.BillingDetailItem> billingDetails = new ArrayList<>();

			for (ListOcrResponse.ExpenseListItem sourceItem : data.getExpenseList()) {
				ListOcrVO.BillingDetailItem targetItem = new ListOcrVO.BillingDetailItem();
				targetItem.setProjectName(sourceItem.getName());
				targetItem.setFeeType(sourceItem.getExpenseItem());
				targetItem.setProjectCode(sourceItem.getCode());
				targetItem.setQuantity(sourceItem.getQuantity());
				targetItem.setUnitPrice(sourceItem.getUnitPrice());
				targetItem.setAmount(sourceItem.getAmount());
				billingDetails.add(targetItem);
			}

			vo.setBillingDetails(billingDetails);
		}
	}



}
