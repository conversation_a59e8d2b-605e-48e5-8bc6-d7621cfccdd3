package com.yixun.wid.v2.controller;

import com.yixun.wid.v2.entity.PriceLimit;
import com.yixun.wid.v2.entity.ThreeCatalogue;
import com.yixun.wid.v2.vo.ThreeCatalogueWithPriceLimitsVO;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * ThreeCatalogue与PriceLimit有效性关联测试类
 * 测试getThreeCatalogueWithPriceLimits接口中PriceLimit的effective状态是否正确赋值
 */
public class ThreeCatalogueWithPriceLimitsEffectiveTest {

    /**
     * 获取指定天数前/后的日期
     * @param days 天数，正数表示未来，负数表示过去
     * @return 日期
     */
    private Date getDateByDays(int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return calendar.getTime();
    }

    /**
     * 创建测试用的ThreeCatalogue对象
     * @param effective 是否有效
     * @return ThreeCatalogue对象
     */
    private ThreeCatalogue createTestThreeCatalogue(boolean effective) {
        ThreeCatalogue catalogue = new ThreeCatalogue();
        catalogue.setId(12345L);
        catalogue.setProjectName("测试药品");
        catalogue.setLevel("甲");
        catalogue.setType("药品");
        
        if (effective) {
            // 创建有效的三目录（开始日期在过去，无结束日期）
            catalogue.setStartDate(getDateByDays(-30));
            // 无结束日期，长期有效
        } else {
            // 创建无效的三目录（已过期）
            catalogue.setStartDate(getDateByDays(-60));
            catalogue.setEndDate(getDateByDays(-10));
        }
        
        return catalogue;
    }

    /**
     * 创建测试用的PriceLimit列表
     * @param threeCatalogueId 关联的三目录ID
     * @return PriceLimit列表
     */
    private List<PriceLimit> createTestPriceLimits(Long threeCatalogueId) {
        List<PriceLimit> priceLimits = new ArrayList<>();
        
        // 创建三甲医院限价
        PriceLimit priceLimit1 = new PriceLimit();
        priceLimit1.setId(1L);
        priceLimit1.setThreeCatalogueId(threeCatalogueId);
        priceLimit1.setProjectName("测试药品");
        priceLimit1.setPriceLimitLevel("三甲");
        priceLimit1.setMaxPrice(new BigDecimal("100.00"));
        priceLimits.add(priceLimit1);
        
        // 创建三乙医院限价
        PriceLimit priceLimit2 = new PriceLimit();
        priceLimit2.setId(2L);
        priceLimit2.setThreeCatalogueId(threeCatalogueId);
        priceLimit2.setProjectName("测试药品");
        priceLimit2.setPriceLimitLevel("三乙");
        priceLimit2.setMaxPrice(new BigDecimal("80.00"));
        priceLimits.add(priceLimit2);
        
        // 创建二甲医院限价
        PriceLimit priceLimit3 = new PriceLimit();
        priceLimit3.setId(3L);
        priceLimit3.setThreeCatalogueId(threeCatalogueId);
        priceLimit3.setProjectName("测试药品");
        priceLimit3.setPriceLimitLevel("二甲");
        priceLimit3.setMaxPrice(new BigDecimal("60.00"));
        priceLimits.add(priceLimit3);
        
        return priceLimits;
    }

    @Test
    public void testEffectiveTrueAssignment() {
        // 测试：当ThreeCatalogue有效时，PriceLimit的effective应该为true
        
        // 创建有效的三目录
        ThreeCatalogue threeCatalogue = createTestThreeCatalogue(true);
        assert threeCatalogue.getEffective() == true;
        
        // 创建关联的限价目录
        List<PriceLimit> priceLimits = createTestPriceLimits(threeCatalogue.getId());
        
        // 模拟Controller中的逻辑：为每个PriceLimit的effective状态赋值
        Boolean threeCatalogueEffective = threeCatalogue.getEffective();
        for (PriceLimit priceLimit : priceLimits) {
            priceLimit.setEffective(threeCatalogueEffective);
        }
        
        // 验证所有PriceLimit的effective状态都为true
        for (PriceLimit priceLimit : priceLimits) {
            assert priceLimit.getEffective() == true;
        }
        
        // 创建返回对象
        ThreeCatalogueWithPriceLimitsVO result = new ThreeCatalogueWithPriceLimitsVO();
        result.setThreeCatalogue(threeCatalogue);
        result.setPriceLimits(priceLimits);
        
        // 验证结果
        assert result.getThreeCatalogue().getEffective() == true;
        assert result.getPriceLimits().size() == 3;
        
        for (PriceLimit priceLimit : result.getPriceLimits()) {
            assert priceLimit.getEffective() == true;
        }
        
        System.out.println("测试通过：ThreeCatalogue有效时，所有PriceLimit的effective状态都为true");
        System.out.println("  ThreeCatalogue有效性: " + result.getThreeCatalogue().getEffective());
        System.out.println("  PriceLimit数量: " + result.getPriceLimits().size());
        result.getPriceLimits().forEach(pl -> 
            System.out.println("    " + pl.getPriceLimitLevel() + " - 有效性: " + pl.getEffective())
        );
    }

    @Test
    public void testEffectiveFalseAssignment() {
        // 测试：当ThreeCatalogue无效时，PriceLimit的effective应该为false
        
        // 创建无效的三目录
        ThreeCatalogue threeCatalogue = createTestThreeCatalogue(false);
        assert threeCatalogue.getEffective() == false;
        
        // 创建关联的限价目录
        List<PriceLimit> priceLimits = createTestPriceLimits(threeCatalogue.getId());
        
        // 模拟Controller中的逻辑：为每个PriceLimit的effective状态赋值
        Boolean threeCatalogueEffective = threeCatalogue.getEffective();
        for (PriceLimit priceLimit : priceLimits) {
            priceLimit.setEffective(threeCatalogueEffective);
        }
        
        // 验证所有PriceLimit的effective状态都为false
        for (PriceLimit priceLimit : priceLimits) {
            assert priceLimit.getEffective() == false;
        }
        
        // 创建返回对象
        ThreeCatalogueWithPriceLimitsVO result = new ThreeCatalogueWithPriceLimitsVO();
        result.setThreeCatalogue(threeCatalogue);
        result.setPriceLimits(priceLimits);
        
        // 验证结果
        assert result.getThreeCatalogue().getEffective() == false;
        assert result.getPriceLimits().size() == 3;
        
        for (PriceLimit priceLimit : result.getPriceLimits()) {
            assert priceLimit.getEffective() == false;
        }
        
        System.out.println("测试通过：ThreeCatalogue无效时，所有PriceLimit的effective状态都为false");
        System.out.println("  ThreeCatalogue有效性: " + result.getThreeCatalogue().getEffective());
        System.out.println("  PriceLimit数量: " + result.getPriceLimits().size());
        result.getPriceLimits().forEach(pl -> 
            System.out.println("    " + pl.getPriceLimitLevel() + " - 有效性: " + pl.getEffective())
        );
    }

    @Test
    public void testEmptyPriceLimitsList() {
        // 测试：当没有关联的PriceLimit时，不应该出现异常
        
        // 创建有效的三目录
        ThreeCatalogue threeCatalogue = createTestThreeCatalogue(true);
        assert threeCatalogue.getEffective() == true;
        
        // 创建空的限价目录列表
        List<PriceLimit> priceLimits = new ArrayList<>();
        
        // 模拟Controller中的逻辑：为每个PriceLimit的effective状态赋值
        Boolean threeCatalogueEffective = threeCatalogue.getEffective();
        for (PriceLimit priceLimit : priceLimits) {
            priceLimit.setEffective(threeCatalogueEffective);
        }
        
        // 创建返回对象
        ThreeCatalogueWithPriceLimitsVO result = new ThreeCatalogueWithPriceLimitsVO();
        result.setThreeCatalogue(threeCatalogue);
        result.setPriceLimits(priceLimits);
        
        // 验证结果
        assert result.getThreeCatalogue().getEffective() == true;
        assert result.getPriceLimits().size() == 0;
        
        System.out.println("测试通过：空PriceLimit列表不会导致异常");
        System.out.println("  ThreeCatalogue有效性: " + result.getThreeCatalogue().getEffective());
        System.out.println("  PriceLimit数量: " + result.getPriceLimits().size());
    }

    @Test
    public void testNullEffectiveHandling() {
        // 测试：处理ThreeCatalogue的effective为null的情况
        
        // 创建三目录但不设置日期（应该返回true）
        ThreeCatalogue threeCatalogue = new ThreeCatalogue();
        threeCatalogue.setId(12345L);
        threeCatalogue.setProjectName("测试药品");
        threeCatalogue.setLevel("甲");
        threeCatalogue.setType("药品");
        // 不设置开始和结束日期，应该永久有效
        
        assert threeCatalogue.getEffective() == true;
        
        // 创建关联的限价目录
        List<PriceLimit> priceLimits = createTestPriceLimits(threeCatalogue.getId());
        
        // 模拟Controller中的逻辑：为每个PriceLimit的effective状态赋值
        Boolean threeCatalogueEffective = threeCatalogue.getEffective();
        for (PriceLimit priceLimit : priceLimits) {
            priceLimit.setEffective(threeCatalogueEffective);
        }
        
        // 验证所有PriceLimit的effective状态都为true
        for (PriceLimit priceLimit : priceLimits) {
            assert priceLimit.getEffective() == true;
        }
        
        System.out.println("测试通过：ThreeCatalogue无日期限制时（永久有效），PriceLimit的effective状态为true");
        System.out.println("  ThreeCatalogue有效性: " + threeCatalogueEffective);
        System.out.println("  PriceLimit数量: " + priceLimits.size());
    }

    @Test
    public void testBusinessScenario() {
        // 测试实际业务场景
        System.out.println("=== 业务场景测试 ===");
        
        // 场景1：有效的药品及其限价
        ThreeCatalogue validDrug = createTestThreeCatalogue(true);
        List<PriceLimit> validPriceLimits = createTestPriceLimits(validDrug.getId());
        
        // 赋值有效性
        Boolean validEffective = validDrug.getEffective();
        for (PriceLimit priceLimit : validPriceLimits) {
            priceLimit.setEffective(validEffective);
        }
        
        System.out.println("场景1：有效药品的限价目录");
        System.out.println("  药品: " + validDrug.getProjectName() + " (有效性: " + validDrug.getEffective() + ")");
        validPriceLimits.forEach(pl -> 
            System.out.println("    " + pl.getPriceLimitLevel() + ": " + pl.getMaxPrice() + "元 (有效性: " + pl.getEffective() + ")")
        );
        
        // 场景2：无效的药品及其限价
        ThreeCatalogue invalidDrug = createTestThreeCatalogue(false);
        List<PriceLimit> invalidPriceLimits = createTestPriceLimits(invalidDrug.getId());
        
        // 赋值有效性
        Boolean invalidEffective = invalidDrug.getEffective();
        for (PriceLimit priceLimit : invalidPriceLimits) {
            priceLimit.setEffective(invalidEffective);
        }
        
        System.out.println("场景2：无效药品的限价目录");
        System.out.println("  药品: " + invalidDrug.getProjectName() + " (有效性: " + invalidDrug.getEffective() + ")");
        invalidPriceLimits.forEach(pl -> 
            System.out.println("    " + pl.getPriceLimitLevel() + ": " + pl.getMaxPrice() + "元 (有效性: " + pl.getEffective() + ")")
        );
        
        // 验证业务逻辑
        assert validDrug.getEffective() == true;
        assert invalidDrug.getEffective() == false;
        
        for (PriceLimit pl : validPriceLimits) {
            assert pl.getEffective() == true;
        }
        for (PriceLimit pl : invalidPriceLimits) {
            assert pl.getEffective() == false;
        }
        
        System.out.println("业务场景测试通过：有效性状态正确传递");
    }
}
