package com.yixun.wid.v2.controller;

import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.v2.vo.ai.SurgicalInformationResponse;
import com.yixun.wid.v2.vo.ai.SurgicalInformationVO;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

/**
 * 手术信息识别状态验证测试类
 * 测试对SurgicalInformationResponse状态的判断逻辑
 */
public class SurgicalInformationStatusValidationTest {

    /**
     * 模拟Controller中的状态检查和映射逻辑
     * @param response AI识别响应结果
     * @param caseId 案件ID
     * @return SurgicalInformationVO对象
     * @throws DataErrorException 当状态不为success时抛出异常
     */
    private SurgicalInformationVO processSurgicalInformationResponse(SurgicalInformationResponse response, Long caseId) 
            throws DataErrorException {
        
        // 检查AI识别结果状态
        if (response.getStatus() == null || response.getStatus().trim().isEmpty() || !"success".equals(response.getStatus())) {
            String errorMsg = String.format("手术信息识别失败：status=%s, message=%s",
                response.getStatus(), response.getMessage());
            throw new DataErrorException(errorMsg);
        }

        // 创建VO对象并映射数据
        SurgicalInformationVO resultVO = new SurgicalInformationVO();
        if (caseId != null) {
            resultVO.setId(caseId);
        }

        // 映射手术名称
        if (response.getData() != null && response.getData().getSurgicalName() != null 
                && !response.getData().getSurgicalName().isEmpty()) {
            resultVO.setSurgicalNames(response.getData().getSurgicalName());
        }

        return resultVO;
    }

    @Test
    public void testSuccessStatus() {
        // 测试：status为success的情况
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus("success");
        response.setMessage("手术信息识别成功");

        SurgicalInformationResponse.SurgicalData data = 
            new SurgicalInformationResponse.SurgicalData();
        data.setSurgicalName(Arrays.asList("阑尾切除术", "胆囊切除术"));
        response.setData(data);

        try {
            SurgicalInformationVO result = processSurgicalInformationResponse(response, 12345L);
            
            // 验证结果
            assert result != null;
            assert result.getId().equals(12345L);
            assert result.getSurgicalNames() != null;
            assert result.getSurgicalNames().size() == 2;
            assert result.getSurgicalNames().contains("阑尾切除术");
            assert result.getSurgicalNames().contains("胆囊切除术");
            
            System.out.println("测试通过：status=success - 正常处理");
            System.out.println("  案件ID: " + result.getId());
            System.out.println("  手术名称: " + result.getSurgicalNames());
        } catch (DataErrorException e) {
            assert false : "不应该抛出异常：status为success";
        }
    }

    @Test
    public void testFailedStatus() {
        // 测试：status为failed的情况
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus("failed");
        response.setMessage("手术信息识别失败");

        try {
            processSurgicalInformationResponse(response, 12345L);
            assert false : "应该抛出异常：status为failed";
        } catch (DataErrorException e) {
            assert e.getMessage().contains("手术信息识别失败");
            assert e.getMessage().contains("status=failed");
            System.out.println("测试通过：status=failed - 正确抛出异常");
            System.out.println("  异常信息: " + e.getMessage());
        }
    }

    @Test
    public void testErrorStatus() {
        // 测试：status为error的情况
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus("error");
        response.setMessage("系统错误");

        try {
            processSurgicalInformationResponse(response, 12345L);
            assert false : "应该抛出异常：status为error";
        } catch (DataErrorException e) {
            assert e.getMessage().contains("手术信息识别失败");
            assert e.getMessage().contains("status=error");
            System.out.println("测试通过：status=error - 正确抛出异常");
            System.out.println("  异常信息: " + e.getMessage());
        }
    }

    @Test
    public void testNullStatus() {
        // 测试：status为null的情况
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus(null);
        response.setMessage("状态为空");

        try {
            processSurgicalInformationResponse(response, 12345L);
            assert false : "应该抛出异常：status为null";
        } catch (DataErrorException e) {
            assert e.getMessage().contains("手术信息识别失败");
            assert e.getMessage().contains("status=null");
            System.out.println("测试通过：status=null - 正确抛出异常");
            System.out.println("  异常信息: " + e.getMessage());
        }
    }

    @Test
    public void testEmptyStatus() {
        // 测试：status为空字符串的情况
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus("");
        response.setMessage("状态为空字符串");

        try {
            processSurgicalInformationResponse(response, 12345L);
            assert false : "应该抛出异常：status为空字符串";
        } catch (DataErrorException e) {
            assert e.getMessage().contains("手术信息识别失败");
            assert e.getMessage().contains("status=");
            System.out.println("测试通过：status=空字符串 - 正确抛出异常");
            System.out.println("  异常信息: " + e.getMessage());
        }
    }

    @Test
    public void testBlankStatus() {
        // 测试：status为空白字符串的情况
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus("   ");
        response.setMessage("状态为空白字符串");

        try {
            processSurgicalInformationResponse(response, 12345L);
            assert false : "应该抛出异常：status为空白字符串";
        } catch (DataErrorException e) {
            assert e.getMessage().contains("手术信息识别失败");
            assert e.getMessage().contains("status=   ");
            System.out.println("测试通过：status=空白字符串 - 正确抛出异常");
            System.out.println("  异常信息: " + e.getMessage());
        }
    }

    @Test
    public void testTimeoutStatus() {
        // 测试：status为timeout的情况
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus("timeout");
        response.setMessage("请求超时");

        try {
            processSurgicalInformationResponse(response, 12345L);
            assert false : "应该抛出异常：status为timeout";
        } catch (DataErrorException e) {
            assert e.getMessage().contains("手术信息识别失败");
            assert e.getMessage().contains("status=timeout");
            System.out.println("测试通过：status=timeout - 正确抛出异常");
            System.out.println("  异常信息: " + e.getMessage());
        }
    }

    @Test
    public void testSuccessWithoutCaseId() {
        // 测试：status为success但没有案件ID的情况（直接传入request对象）
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus("success");
        response.setMessage("手术信息识别成功");

        SurgicalInformationResponse.SurgicalData data = 
            new SurgicalInformationResponse.SurgicalData();
        data.setSurgicalName(Arrays.asList("心脏搭桥手术"));
        response.setData(data);

        try {
            SurgicalInformationVO result = processSurgicalInformationResponse(response, null);
            
            // 验证结果
            assert result != null;
            assert result.getId() == null; // 没有案件ID
            assert result.getSurgicalNames() != null;
            assert result.getSurgicalNames().size() == 1;
            assert result.getSurgicalNames().contains("心脏搭桥手术");
            
            System.out.println("测试通过：status=success且无案件ID - 正常处理");
            System.out.println("  案件ID: " + result.getId() + " (应该为null)");
            System.out.println("  手术名称: " + result.getSurgicalNames());
        } catch (DataErrorException e) {
            assert false : "不应该抛出异常：status为success";
        }
    }

    @Test
    public void testSuccessWithEmptyData() {
        // 测试：status为success但数据为空的情况
        SurgicalInformationResponse response = new SurgicalInformationResponse();
        response.setStatus("success");
        response.setMessage("手术信息识别成功");

        SurgicalInformationResponse.SurgicalData data = 
            new SurgicalInformationResponse.SurgicalData();
        data.setSurgicalName(Arrays.asList()); // 空列表
        response.setData(data);

        try {
            SurgicalInformationVO result = processSurgicalInformationResponse(response, 99999L);
            
            // 验证结果
            assert result != null;
            assert result.getId().equals(99999L);
            assert result.getSurgicalNames() == null; // 空列表不会被设置
            
            System.out.println("测试通过：status=success但数据为空 - 正常处理");
            System.out.println("  案件ID: " + result.getId());
            System.out.println("  手术名称: " + result.getSurgicalNames() + " (应该为null)");
        } catch (DataErrorException e) {
            assert false : "不应该抛出异常：status为success";
        }
    }

    @Test
    public void testBusinessScenarios() {
        // 测试实际业务场景
        System.out.println("=== 业务场景测试 ===");
        
        // 场景1：AI识别成功，返回多个手术
        SurgicalInformationResponse successResponse = new SurgicalInformationResponse();
        successResponse.setStatus("success");
        successResponse.setMessage("识别成功");
        
        SurgicalInformationResponse.SurgicalData successData = 
            new SurgicalInformationResponse.SurgicalData();
        successData.setSurgicalName(Arrays.asList("腹腔镜下胆囊切除术", "疝气修补术"));
        successResponse.setData(successData);

        try {
            SurgicalInformationVO result = processSurgicalInformationResponse(successResponse, 2001L);
            assert result.getSurgicalNames().size() == 2;
            System.out.println("场景1通过：AI识别成功 - " + result.getSurgicalNames());
        } catch (DataErrorException e) {
            assert false : "场景1失败：不应该抛出异常";
        }
        
        // 场景2：AI识别失败，网络错误
        SurgicalInformationResponse failedResponse = new SurgicalInformationResponse();
        failedResponse.setStatus("network_error");
        failedResponse.setMessage("网络连接失败");

        try {
            processSurgicalInformationResponse(failedResponse, 2002L);
            assert false : "场景2失败：应该抛出异常";
        } catch (DataErrorException e) {
            assert e.getMessage().contains("network_error");
            System.out.println("场景2通过：网络错误 - 正确抛出异常");
        }
        
        // 场景3：AI识别超时
        SurgicalInformationResponse timeoutResponse = new SurgicalInformationResponse();
        timeoutResponse.setStatus("timeout");
        timeoutResponse.setMessage("AI服务响应超时");

        try {
            processSurgicalInformationResponse(timeoutResponse, 2003L);
            assert false : "场景3失败：应该抛出异常";
        } catch (DataErrorException e) {
            assert e.getMessage().contains("timeout");
            System.out.println("场景3通过：AI服务超时 - 正确抛出异常");
        }
    }
}
