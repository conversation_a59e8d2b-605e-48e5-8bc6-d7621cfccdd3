package com.yixun.wid.v2.utils;

import com.yixun.wid.v2.vo.ai.*;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;

/**
 * AI工具类测试
 */
@SpringBootTest
@TestPropertySource(properties = {
    "api.llmsBaseUrl=https://4y8mfmz6n5.apifox.cn"
})
public class AiUtilsTest {

    @Resource
    private AiUtils aiUtils;

    @Test
    public void testQuerySimilarity() {
        try {
            QuerySimilarityResponse response = aiUtils.querySimilarity(
                Collections.singletonList("你好"),
                Arrays.asList("你好", "我好", "不起", "对不起"),
                3,
                true
            );
            
            System.out.println("相似查找结果: " + response);
            assert response != null;
            assert "success".equals(response.getStatus());
        } catch (Exception e) {
            System.err.println("相似查找测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testMaterialClassification() {
        try {
            MaterialClassificationResponse response = aiUtils.materialClassification(
                Arrays.asList(
                    "https://indeclare-app-dev.yxgsyf.com/indeclare/2025/06/1750068660915_untitled(5).png",
                    "https://indeclare-app-dev.yxgsyf.com/indeclare/2025/06/1750068721594_snipaste_2025-06-03_11-09-49 1.png"
                ),
                "medical"
            );
            
            System.out.println("材料分类结果: " + response);
            assert response != null;
        } catch (Exception e) {
            System.err.println("材料分类测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testCalculator() {
        try {
            CalculatorResponse response = aiUtils.calculator("1+1");
            
            System.out.println("计算器结果: " + response);
            assert response != null;
        } catch (Exception e) {
            System.err.println("计算器测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testProjectSearch() {
        try {
            ProjectSearchResponse response = aiUtils.projectSearch("医疗项目");
            
            System.out.println("项目查询结果: " + response);
            assert response != null;
        } catch (Exception e) {
            System.err.println("项目查询测试失败: " + e.getMessage());
        }
    }
}
