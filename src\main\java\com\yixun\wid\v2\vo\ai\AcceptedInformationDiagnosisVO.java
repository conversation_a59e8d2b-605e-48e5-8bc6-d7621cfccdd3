package com.yixun.wid.v2.vo.ai;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 受理信息和临床诊断信息识别响应VO
 * 用于返回AI识别的受理信息和临床诊断数据，不直接返回完整的MedicalCases对象
 */
@Data
public class AcceptedInformationDiagnosisVO {

    /**
     * 案件ID（如果是基于现有案件的识别）
     */
    private Long id;

    /**
     * 事故日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date accidentDate;

    /**
     * 职工姓名
     */
    private String workerName;

    /**
     * 性别
     */
    private String gender;

    /**
     * 身份证号码
     */
    private String idCard;

    /**
     * 用人单位名称
     */
    private String organization;

    /**
     * 工伤诊断列表（临床诊断）
     */
    private List<String> injuryDiagnoses;
}
