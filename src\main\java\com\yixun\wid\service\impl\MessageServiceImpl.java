package com.yixun.wid.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yixun.bean.in.MessageUpdateIn;
import com.yixun.wid.bean.in.MessageSaveIn;
import com.yixun.wid.bean.in.SaveBatchMessageIn;
import com.yixun.wid.bean.in.WxMiniSendIn;
import com.yixun.wid.bean.other.MessageData;
import com.yixun.wid.bean.other.MessageDefine;
import com.yixun.wid.entity.Declaration;
import com.yixun.wid.entity.User;

import com.yixun.wid.service.MessageService;
import com.yixun.wid.service.UserService;
import com.yixun.wid.utils.BeanUtils;
import com.yixun.wid.utils.OkHttpKit;
import com.yixun.wid.utils.StringHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

@Slf4j
@Service
public class MessageServiceImpl implements MessageService {

    @Value("${api.messageCenterApi}")
    private String messageCenterApi;

    @Value("${weixin.corporation.appId}")
    private String appId;

    @Value("${weixin.corporation.appSecret}")
    private String appSecret;

    @Value("${weixin.corporation.appletsSubType}")
    private String appletsSubType;

    @Value("${weixin.corporation.appSubTempId}")
    private String appSubTempId;

    @Value("${weixin.corporation.appletsSubToken}")
    private String appletsSubToken;

    @Value("${weixin.corporation.appletsSubAesKey}")
    private String appletsSubAesKey;

    @Value("${message.smsFlag}")
    private Integer smsFlag;

    @Value("${message.instationFlag}")
    private Integer instationFlag;

    @Value("${message.msRecordFlag}")
    private Integer msRecordFlag;

    @Value("${message.wxMsFlag}")
    private Integer wxMsFlag;

    @Value("${message.appId}")
    private String msgAppId;

    @Resource
    private UserService userService;

    private String token;

    @PostConstruct
    public void init() {
        // try {
            token = "{\"appId\":" + msgAppId + ",\"appSecretEncrypt\":\"ed236bc63fa110be59cec159974c4530\"}";
        // } catch (Exception e) {
            // log.error("初始化token失败", e);
//        }
    }

    @Override
    @Async
    public void sendAppMessage(Long userId, String title, String content, Integer type, String redirectUrl, String businessType, String businessParam) {
        try {
            if (instationFlag == 1) {

                if (ObjectUtil.isNull(userId)) {
                    log.warn("用户id为空，跳过消息发送：{}, {}, {}, {}, {}, {}", title, content, type, redirectUrl, businessType, businessParam);
                    return;
                }

                Map<String, Object> params = new HashMap<>();
                params.put("appId", msgAppId);
                params.put("userId", userId);
                params.put("type", type);
                params.put("title", title);
                params.put("content", content);
                params.put("redirectUrl", redirectUrl);
                params.put("businessType", businessType);
                params.put("businessParam", businessParam);
                try {
                    JSONObject connGet = OkHttpKit.apiPostWithToken(messageCenterApi + "/api/message/save",
                            JSON.toJSONString(params), token);
                    if (connGet.containsKey("code") && connGet.get("code").equals(200)) {
                        log.info(JSON.toJSONString(connGet));
                    } else {
                        log.error("发送应用消息失败：{}", connGet.getString("msg"));
                    }
                } catch (IOException e) {
                    log.error("发送应用消息IO异常", e);
                }
            }
        } catch (Exception e) {
            log.error("发送应用消息异常", e);
        }
    }

    @Override
    public void sendBatchMessage(SaveBatchMessageIn saveBatchMessageIn) {
        try {
            if (instationFlag == 1) {
                try {
                    JSONObject connGet = OkHttpKit.apiPostWithToken(messageCenterApi + "/api/message/saveBatch",
                            JSON.toJSONString(saveBatchMessageIn), token);
                    if (connGet != null && connGet.containsKey("code") && connGet.get("code").equals(200)) {
                        log.info(JSON.toJSONString(connGet));
                    } else {
                        log.error("批量发送消息失败：{}", connGet.getString("msg"));
                    }
                } catch (IOException e) {
                    log.error("批量发送消息IO异常", e);
                }
            }
        } catch (Exception e) {
            log.error("批量发送消息异常", e);
        }
    }

    @Override
    public void sendAppTask(String taskType, String title, Declaration declaration) {
        try {
            if (instationFlag == 1) {
                Integer type = 2;
                String redirectUrl = "/pages/progress/declarationDetail/declarationDetail?id=" + declaration.getId() + "&casesId=" + declaration.getCasesId();
                MessageDefine messageDefine = new MessageDefine();
                BeanUtils.copyProperties(declaration, messageDefine);
                try {
                    this.sendAppMessage(declaration.getSubmitUserId(), title, null, type, redirectUrl, taskType, new ObjectMapper().writeValueAsString(messageDefine));
                } catch (Exception e) {
                    log.error("json转换出错", e);
                }
            }
        } catch (Exception e) {
            log.error("发送应用任务消息异常", e);
        }
    }

    @Override
    public void setFinished(MessageUpdateIn messageUpdateIn) {
        try {
            if (instationFlag == 1) {
                messageUpdateIn.setAppId(msgAppId);
                try {
                    JSONObject connGet = OkHttpKit.apiPostWithToken(messageCenterApi + "/api/message/setFinished",
                            JSON.toJSONString(messageUpdateIn), token);
                    if (connGet != null && connGet.containsKey("code") && connGet.get("code").equals(200)) {
                        log.info(JSON.toJSONString(connGet));
                    } else {
                        log.error("设置消息完成状态失败：{}", connGet.getString("msg"));
                    }
                } catch (IOException e) {
                    log.error("设置待办或消息IO异常", e);
                }
            }
        } catch (Exception e) {
            log.error("设置消息完成状态异常", e);
        }
    }

    @Override
    public void sendWxMessage(WxMiniSendIn wxMiniSendIn) {
        try {
            if (wxMsFlag == 1) {
                wxMiniSendIn.setAppletsAppid(appId);
                wxMiniSendIn.setAppletsSecret(appSecret);
                wxMiniSendIn.setAppletsSubType(appletsSubType);
                wxMiniSendIn.setAppSubTempId(appSubTempId);
                wxMiniSendIn.setAppletsSubToken(appletsSubToken);
                wxMiniSendIn.setAppletsSubAesKey(appletsSubAesKey);
                wxMiniSendIn.setAppletsSubDataFormat("JSON");
                wxMiniSendIn.setAppSubPagePath("pages/message/message");
                try {
                    JSONObject connGet = OkHttpKit.apiPostWithToken(messageCenterApi + "/api/wxMiniSms/sendSms",
                            JSON.toJSONString(wxMiniSendIn), token);
                    if (connGet != null && connGet.containsKey("code") && connGet.get("code").equals(200)) {
                        log.info(JSON.toJSONString(connGet));
                    } else {
                        log.error("订阅消息发送失败：{}, {}", connGet.getString("msg"), wxMiniSendIn);
                    }
                } catch (IOException e) {
                    log.error("订阅消息发送IO异常", e);
                }
            }
        } catch (Exception e) {
            log.error("发送微信消息异常", e);
        }
    }

    @Override
    public void sendTrueWxMessage(Long userId, String msType, String msTitle, String msContent) {
        try {
            if (wxMsFlag == 1) {

                if (ObjectUtil.isNull(userId)) {
                    log.warn("用户id为空，跳过消息发送：{}, {}, {}", msType, msTitle, msContent);
                    return;
                }

                //发送订阅消息
                WxMiniSendIn wxMiniSendIn = new WxMiniSendIn();
                //查询openId
                User user = userService.getUserById(userId);
                List<String> openIdList = new ArrayList<>();
                openIdList.add(user.getWeixinMiniOpenId());
                wxMiniSendIn.setOpenIdList(openIdList);
                List<MessageData> wxMaSubMsgList = new ArrayList<>();
                MessageData messageData1 = new MessageData();
                messageData1.setName("phrase1");
                messageData1.setValue(StringHelper.truncateString(msType, 5));
                wxMaSubMsgList.add(messageData1);
                MessageData messageData2 = new MessageData();
                messageData2.setName("thing2");
                messageData2.setValue(StringHelper.truncateString(msTitle, 20));
                wxMaSubMsgList.add(messageData2);
                MessageData messageData3 = new MessageData();
                messageData3.setName("thing4");
                messageData3.setValue(StringHelper.truncateString(msContent, 20));
                wxMaSubMsgList.add(messageData3);
                MessageData messageData4 = new MessageData();
                messageData4.setName("time5");
                messageData4.setValue(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_FORMAT));
                wxMaSubMsgList.add(messageData4);
                wxMiniSendIn.setWxMaSubMsgList(wxMaSubMsgList);
                this.sendWxMessage(wxMiniSendIn);
            }
        } catch (Exception e) {
            log.error("发送真实微信消息异常", e);
        }
    }

    @Override
    public void sendAdminMessage(List<Long> userIds, String title, String content, Integer type, String redirectUrl, String businessType, String businessParam) {
        try {
            if (instationFlag == 1) {
                SaveBatchMessageIn saveBatchMessageIn = new SaveBatchMessageIn();
                saveBatchMessageIn.setAppId(msgAppId);
                List<MessageSaveIn> messages = new ArrayList<>();
                userIds.forEach(e -> {
                    MessageSaveIn messageSaveIn = new MessageSaveIn();
                    messageSaveIn.setTitle(title);
                    messageSaveIn.setContent(content);
                    messageSaveIn.setBusinessType(businessType);
                    messageSaveIn.setBusinessParam(businessParam);
                    messageSaveIn.setRedirectUrl(redirectUrl);
                    messageSaveIn.setUserId(e + "");
                    messageSaveIn.setAppId(msgAppId);
                    messageSaveIn.setType(1);
                    messages.add(messageSaveIn);
                });
                saveBatchMessageIn.setMessages(messages);
                this.sendBatchMessage(saveBatchMessageIn);
            }
        } catch (Exception e) {
            log.error("发送管理员消息异常", e);
        }
    }

}
