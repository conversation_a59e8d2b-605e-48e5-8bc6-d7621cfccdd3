package com.yixun.wid.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.bean.CommonPage;
import com.yixun.wid.bean.in.AdministratorGetIn;
import com.yixun.wid.bean.out.AdministratorOut;
import com.yixun.wid.mapper.AdministratorMapper;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.service.AdministratorService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AdministratorServiceImpl extends ServiceImpl<AdministratorMapper, Administrator> implements AdministratorService {

    @Resource
    private AdministratorMapper administratorMapper;

    @Override
    public Administrator getAdministratorById(Long id) {
        return administratorMapper.selectById(id);
    }

    @Override
    public void insert(Administrator administrator) {
        administratorMapper.insert(administrator);
    }

    @Override
    public void update(Administrator administrator) {
        administratorMapper.updateById(administrator);
    }

    @Override
    public Administrator getAdministratorByName(String username) {
        QueryWrapper<Administrator> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        List<Administrator> administrators = administratorMapper.selectList(queryWrapper);
        if (administrators.isEmpty()) {
            return null;
        }
        return administrators.get(0);
    }

    @Override
    public List<Administrator> getUserList() {
        return administratorMapper.selectList(null);
    }

    @Override
    public Page<Administrator> getAdminPageList(AdministratorGetIn administratorGetIn, CommonPage commonPage) {
        QueryWrapper<Administrator> queryWrapper = new QueryWrapper<>();
        if (administratorGetIn.getUsername() != null) {
            queryWrapper.like("username", administratorGetIn.getUsername());
        }
        if (administratorGetIn.getRealName() != null) {
            queryWrapper.like("real_name", administratorGetIn.getRealName());
        }
        if (administratorGetIn.getPhone() != null) {
            queryWrapper.like("phone", administratorGetIn.getPhone());
        }
        if (administratorGetIn.getEmail() != null) {
            queryWrapper.like("email", administratorGetIn.getEmail());
        }
        if (administratorGetIn.getType() != null) {
            queryWrapper.eq("type", administratorGetIn.getType().name());
        }
        if (administratorGetIn.getGovernment() != null) {
            queryWrapper.eq("government", administratorGetIn.getGovernment());
        }

        Page page = new Page(commonPage.getPageNum(), commonPage.getPageSize());
        return (Page<Administrator>) administratorMapper.selectPage(page, queryWrapper);
    }

    @Override
    public void delete(Long administratorId) {
        administratorMapper.deleteById(administratorId);
    }

    @Override
    public Page<AdministratorOut> getAdministratorPageList(AdministratorGetIn administratorGetIn, CommonPage commonPage) {
        Page page = new Page(commonPage.getPageNum(), commonPage.getPageSize());
        return administratorMapper.getAdministratorPageList(page, administratorGetIn.getUsername(),
                administratorGetIn.getEmail(), administratorGetIn.getRealName(), administratorGetIn.getPhone(),
                administratorGetIn.getType() == null ? null : administratorGetIn.getType().name(), administratorGetIn.getGovernment());
    }

	@Override
	public Administrator getByPhoneType(String phone, String type) {
		QueryWrapper<Administrator> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("phone", phone);
		queryWrapper.eq("type", type);
		List<Administrator> administrators = administratorMapper.selectList(queryWrapper);
		if (administrators.isEmpty()) {
			return null;
		}
		return administrators.get(0);
	}
}
