package com.yixun.wid.v2.bean.in;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 账单明细查询输入参数
 */
@Data
public class BillingDetailQueryIn {
    
    /**
     * 账单ID
     */
    @ApiModelProperty(value = "账单ID", required = true)
    @NotNull(message = "账单ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long billingInfoId;
    
    /**
     * 费用类别（可选）
     */
    @ApiModelProperty(value = "费用类别", required = false)
    private String feeType;
}