package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yixun.wid.v2.vo.ai.AcceptedInformationDiagnosisRequest;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.yixun.wid.v2.validation.IdCard;

/**
 * 工伤待遇业务
 */
@Data
public class MedicalCases {

    /**
     * 主键id
     */
    @Id
    private Long id;

    /**
     * 案件编号
     * 格式：BX1014受理日期+序号，例：BX101420250604001
     */
    private String caseNumber;

    /**
     * 业务办理状态
     */
    private MedicalCasesStatus status;

    // 受理信息

    /**
     * 职工姓名
     */
    private String workerName;

    /**
     * 用人单位名称
     */
    private String organization;

    /**
     * 身份证
     */
    @IdCard(message = "身份证号码格式不正确")
    private String idCard;

    /**
     * 性别
     */
    private String gender;

    /**
     * 是否参保
     */
    private Boolean hasInsurance;

    /**
     * 参保地
     */
    private String insuranceAddress;

    /**
     * 业务申请时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTime;

    /**
     * 事故日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date accidentDate;

    /**
     * 受理日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date acceptDate;

    /**
     * 治疗类型
     */
    private String treatmentType;

    /**
     * 申请业务列表
     */
    private List<String> businessTypes;

    // 诊断信息

    /**
     * 工伤诊断列表
     */
    private List<String> injuryDiagnoses;

    /**
     * 手术信息列表
     */
    private List<String> surgeryInfos;

    /**
     * 账单信息列表，用于前端传递和返回数据，不会持久化到数据库
     */
    @Transient
    private List<BillingInfo> billingInfos;

    /**
     * 待遇材料
     */
    private List<TreatmentMaterials> treatmentMaterials;

	/**
	 * 待遇材料是否提交 为null或false表示未提交，true表示已提交
	 */
	private Boolean treatmentMaterialsSubmit;

	/**
	 * 材料类型识别响应数据存储，不持久化到数据库，基于就诊材料动态生成
	 */
	@Transient
	private AcceptedInformationDiagnosisRequest acceptedInformationDiagnosisRequest;

    /**
     * 任务接收人id
     */
    private String assigneeUserId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;

    /**
     * 创建用户id
     */
    private String createUserId;

    /**
     * 创建用户名
     */
    private String createUserName;

    /**
     * 最近操作用户id
     */
    private String recentUserId;

    /**
     * 最近操作用户名
     */
    private String recentUserName;

    /**
     * 提交用户id
     */
    private String submitUserId;

    /**
     * 提交用户名称
     */
    private String submitUserName;

    /**
     * 受理用户id
     */
    private String acceptUserId;

    /**
     * 受理用户名称
     */
    private String acceptUserName;

	/**
	 * 受理时间
	 */
	@JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date acceptTime;

    /**
     * 初审用户id
     */
    private String preReviewUserId;

    /**
     * 初审用户名
     */
    private String preReviewUserName;

    /**
     * 初审时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date preReviewTime;

    /**
     * 复审用户id
     */
    private String reviewUserId;

    /**
     * 复审用户名
     */
    private String reviewUserName;

    /**
     * 复审时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date reviewTime;

    /**
     * 终审用户id
     */
    private String finalReviewUserId;

    /**
     * 终审用户名
     */
    private String finalReviewUserName;

    /**
     * 终审时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date finalReviewTime;

    /**
     * 是否为提交操作
     */
    private Boolean isSubmit;

	/**
	 * 理算结果 map结构 key为ResponsibilityTree的code
	 */
    private Map<String, ClaimsInformation> claimsInformations;

    /**
     * 理算条信息
     */
    @Data
    public static class ClaimsInformation {

        /**
         * 责任树编码
         */
        private String responsibilityTreeCode;

        /**
         * 理算状态（true：已理算，false：未理算）
         */
        private Boolean claimsStatus;

        /**
         * 理算信息数据
         */
        private ClaimsData claimsData;

        /**
         * 理算结果信息
         */
        private ClaimsResult claimsResult;

        /**
         * 门诊核销明细
         */
        private OutpatientClearing outpatientClearing;

        /**
         * 住院核销明细
         */
        private HospitalClearing hospitalClearing;

    }

    /**
     * 动态生成AcceptedInformationDiagnosisRequest对象，基于就诊材料
     * 跳过类型为"其他未识别"的材料
     */
    public AcceptedInformationDiagnosisRequest getAcceptedInformationDiagnosisRequest() {
        if (treatmentMaterials == null || treatmentMaterials.isEmpty()) {
            return null;
        }

        AcceptedInformationDiagnosisRequest request = new AcceptedInformationDiagnosisRequest();

        // 生成申请材料部分
        AcceptedInformationDiagnosisRequest.ApplicationMaterials applicationMaterials =
            generateApplicationMaterials();
        if (applicationMaterials != null) {
            request.setApplication(applicationMaterials);
        }

        // 生成就诊材料部分
        List<AcceptedInformationDiagnosisRequest.MedicalVisit> medicalVisits =
            generateMedicalVisits();
        if (medicalVisits != null && !medicalVisits.isEmpty()) {
            request.setMedical(medicalVisits);
        }

        return request;
    }

    /**
     * 生成申请材料部分
     */
    private AcceptedInformationDiagnosisRequest.ApplicationMaterials generateApplicationMaterials() {
        AcceptedInformationDiagnosisRequest.ApplicationMaterials applicationMaterials =
            new AcceptedInformationDiagnosisRequest.ApplicationMaterials();

        boolean hasApplicationMaterials = false;

        for (TreatmentMaterials treatmentMaterial : treatmentMaterials) {
            if (treatmentMaterial.getFileList() != null) {
                for (TreatmentMaterials.TreatmentFile file : treatmentMaterial.getFileList()) {
                    if (file.getFileType() == null || "其他未识别".equals(file.getFileType())) {
                        continue; // 跳过类型为"其他未识别"的材料
                    }

                    String fileUrl = file.getUrl();
                    if (fileUrl == null) {
	                    continue;
                    }

                    // 根据文件类型添加到对应的申请材料列表中
                    switch (file.getFileType()) {
                        case "工伤待遇申请表":
                            applicationMaterials.setWorkInjuryBenefitApplicationForm(
                                addToList(applicationMaterials.getWorkInjuryBenefitApplicationForm(), fileUrl));
                            hasApplicationMaterials = true;
                            break;
                        case "工伤医疗(康复)待遇申请表":
                            applicationMaterials.setWorkInjuryMedicalRehabilitationBenefitApplicationForm(
                                addToList(applicationMaterials.getWorkInjuryMedicalRehabilitationBenefitApplicationForm(), fileUrl));
                            hasApplicationMaterials = true;
                            break;
                        case "工伤决定书":
                            applicationMaterials.setWorkInjuryDeterminationDocument(
                                addToList(applicationMaterials.getWorkInjuryDeterminationDocument(), fileUrl));
                            hasApplicationMaterials = true;
                            break;
                        case "工伤收件清单":
                            applicationMaterials.setWorkInjuryReceiptList(
                                addToList(applicationMaterials.getWorkInjuryReceiptList(), fileUrl));
                            hasApplicationMaterials = true;
                            break;
                        case "身份证":
                            applicationMaterials.setIdCard(
                                addToList(applicationMaterials.getIdCard(), fileUrl));
                            hasApplicationMaterials = true;
                            break;
                        case "社会保障卡":
                            applicationMaterials.setSocialSecurityCard(
                                addToList(applicationMaterials.getSocialSecurityCard(), fileUrl));
                            hasApplicationMaterials = true;
                            break;
                        case "银行卡":
                            applicationMaterials.setBankCard(
                                addToList(applicationMaterials.getBankCard(), fileUrl));
                            hasApplicationMaterials = true;
                            break;
                        case "初次(复查)鉴定结论书":
                            applicationMaterials.setInitialReexaminationAppraisalConclusionDocument(
                                addToList(applicationMaterials.getInitialReexaminationAppraisalConclusionDocument(), fileUrl));
                            hasApplicationMaterials = true;
                            break;
                        case "非税收入一般缴款书":
                            applicationMaterials.setNonTaxRevenueGeneralPaymentDocument(
                                addToList(applicationMaterials.getNonTaxRevenueGeneralPaymentDocument(), fileUrl));
                            hasApplicationMaterials = true;
                            break;
                    }
                }
            }
        }

        return hasApplicationMaterials ? applicationMaterials : null;
    }

    /**
     * 生成就诊材料部分
     */
    private List<AcceptedInformationDiagnosisRequest.MedicalVisit> generateMedicalVisits() {
        Map<String, AcceptedInformationDiagnosisRequest.MedicalVisit> visitMap = new HashMap<>();

        for (TreatmentMaterials treatmentMaterial : treatmentMaterials) {
            if (treatmentMaterial.getFileList() != null) {
                for (TreatmentMaterials.TreatmentFile file : treatmentMaterial.getFileList()) {
                    if (file.getFileType() == null || "其他未识别".equals(file.getFileType())) {
                        continue; // 跳过类型为"其他未识别"的材料
                    }

                    String fileUrl = file.getUrl();
                    if (fileUrl == null) {
	                    continue;
                    }

                    // 检查是否为就诊材料类型
                    if (isMedicalMaterialType(file.getFileType())) {
                        String visitKey = treatmentMaterial.getVisitType() + "_" + treatmentMaterial.getVisitDate();
                        AcceptedInformationDiagnosisRequest.MedicalVisit visit = visitMap.get(visitKey);

                        if (visit == null) {
                            visit = new AcceptedInformationDiagnosisRequest.MedicalVisit();
                            visit.setVisitType(treatmentMaterial.getVisitType());
                            visit.setVisitDate(treatmentMaterial.getVisitDate());
                            visit.setMaterial(new AcceptedInformationDiagnosisRequest.MedicalMaterials());
                            visitMap.put(visitKey, visit);
                        }

                        addFileToMedicalMaterials(visit.getMaterial(), file.getFileType(), fileUrl);
                    }
                }
            }
        }

        return visitMap.isEmpty() ? null : new ArrayList<>(visitMap.values());
    }

    /**
     * 判断是否为就诊材料类型
     */
    private boolean isMedicalMaterialType(String fileType) {
        return "病历".equals(fileType) || "病情证明书".equals(fileType) || "出院证明书".equals(fileType) ||
               "出院记录".equals(fileType) || "住院病案首页".equals(fileType) || "诊断证明书".equals(fileType) ||
               "入院证".equals(fileType) || "入院记录".equals(fileType) || "处方签".equals(fileType) ||
               "体温单".equals(fileType) || "病程记录".equals(fileType) || "手术记录".equals(fileType) ||
               "合格证".equals(fileType) || "清单".equals(fileType) || "电子发票".equals(fileType) ||
               "非电子发票".equals(fileType) || "追回单".equals(fileType) || "检查报告".equals(fileType) ||
               "医嘱单".equals(fileType) || "其他就诊报告".equals(fileType);
    }

    /**
     * 将文件添加到就诊材料中
     */
    private void addFileToMedicalMaterials(AcceptedInformationDiagnosisRequest.MedicalMaterials materials,
                                          String fileType, String fileUrl) {
        switch (fileType) {
            case "病历":
                if (materials.getMedicalRecord() == null) {
                    materials.setMedicalRecord(new AcceptedInformationDiagnosisRequest.MedicalRecord());
                }
                materials.getMedicalRecord().setMedicalRecordSubcategory(
                    addToList(materials.getMedicalRecord().getMedicalRecordSubcategory(), fileUrl));
                break;
            case "病情证明书":
                if (materials.getMedicalRecord() == null) {
                    materials.setMedicalRecord(new AcceptedInformationDiagnosisRequest.MedicalRecord());
                }
                materials.getMedicalRecord().setMedicalConditionCertificate(
                    addToList(materials.getMedicalRecord().getMedicalConditionCertificate(), fileUrl));
                break;
            case "出院证明书":
                if (materials.getMedicalRecord() == null) {
                    materials.setMedicalRecord(new AcceptedInformationDiagnosisRequest.MedicalRecord());
                }
                materials.getMedicalRecord().setDischargeCertificate(
                    addToList(materials.getMedicalRecord().getDischargeCertificate(), fileUrl));
                break;
            case "出院记录":
                if (materials.getMedicalRecord() == null) {
                    materials.setMedicalRecord(new AcceptedInformationDiagnosisRequest.MedicalRecord());
                }
                materials.getMedicalRecord().setDischargeRecord(
                    addToList(materials.getMedicalRecord().getDischargeRecord(), fileUrl));
                break;
            case "住院病案首页":
                if (materials.getMedicalRecord() == null) {
                    materials.setMedicalRecord(new AcceptedInformationDiagnosisRequest.MedicalRecord());
                }
                materials.getMedicalRecord().setInpatientMedicalRecordFirstPage(
                    addToList(materials.getMedicalRecord().getInpatientMedicalRecordFirstPage(), fileUrl));
                break;
            case "诊断证明书":
                if (materials.getMedicalRecord() == null) {
                    materials.setMedicalRecord(new AcceptedInformationDiagnosisRequest.MedicalRecord());
                }
                materials.getMedicalRecord().setDiagnosisCertificate(
                    addToList(materials.getMedicalRecord().getDiagnosisCertificate(), fileUrl));
                break;
            case "入院证":
                if (materials.getMedicalRecord() == null) {
                    materials.setMedicalRecord(new AcceptedInformationDiagnosisRequest.MedicalRecord());
                }
                materials.getMedicalRecord().setAdmissionCertificate(
                    addToList(materials.getMedicalRecord().getAdmissionCertificate(), fileUrl));
                break;
            case "入院记录":
                if (materials.getMedicalRecord() == null) {
                    materials.setMedicalRecord(new AcceptedInformationDiagnosisRequest.MedicalRecord());
                }
                materials.getMedicalRecord().setAdmissionRecord(
                    addToList(materials.getMedicalRecord().getAdmissionRecord(), fileUrl));
                break;
            case "处方签":
                if (materials.getMedicalRecord() == null) {
                    materials.setMedicalRecord(new AcceptedInformationDiagnosisRequest.MedicalRecord());
                }
                materials.getMedicalRecord().setPrescriptionSlip(
                    addToList(materials.getMedicalRecord().getPrescriptionSlip(), fileUrl));
                break;
            case "体温单":
                if (materials.getMedicalRecord() == null) {
                    materials.setMedicalRecord(new AcceptedInformationDiagnosisRequest.MedicalRecord());
                }
                materials.getMedicalRecord().setTemperatureSheet(
                    addToList(materials.getMedicalRecord().getTemperatureSheet(), fileUrl));
                break;
            case "病程记录":
                if (materials.getMedicalRecord() == null) {
                    materials.setMedicalRecord(new AcceptedInformationDiagnosisRequest.MedicalRecord());
                }
                materials.getMedicalRecord().setProgressNote(
                    addToList(materials.getMedicalRecord().getProgressNote(), fileUrl));
                break;
            case "手术记录":
                materials.setSurgicalRecord(addToList(materials.getSurgicalRecord(), fileUrl));
                break;
            case "合格证":
                materials.setCertificate(addToList(materials.getCertificate(), fileUrl));
                break;
            case "清单":
                materials.setList(addToList(materials.getList(), fileUrl));
                break;
            case "电子发票":
                materials.setElectronicInvoice(addToList(materials.getElectronicInvoice(), fileUrl));
                break;
            case "非电子发票":
                materials.setNonElectronicInvoice(addToList(materials.getNonElectronicInvoice(), fileUrl));
                break;
            case "追回单":
                materials.setRecallNotice(addToList(materials.getRecallNotice(), fileUrl));
                break;
            case "检查报告":
                materials.setExaminationReport(addToList(materials.getExaminationReport(), fileUrl));
                break;
            case "医嘱单":
                materials.setDoctorOrderSheet(addToList(materials.getDoctorOrderSheet(), fileUrl));
                break;
            case "其他就诊报告":
                materials.setOtherConsultationReports(addToList(materials.getOtherConsultationReports(), fileUrl));
                break;
        }
    }

    /**
     * 向列表中添加元素，如果列表为空则创建新列表
     */
    private List<String> addToList(List<String> list, String item) {
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(item);
        return list;
    }

}
