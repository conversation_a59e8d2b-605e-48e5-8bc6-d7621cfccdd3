package com.yixun.wid.v2.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.idev.excel.EasyExcel;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.controller.ThreeCatalogueController;
import com.yixun.wid.v2.entity.PriceLimit;
import com.yixun.wid.v2.entity.ThreeCatalogue;
import com.yixun.wid.v2.vo.ThreeCatalogueImportVO;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.Objects;

/**
 * 三目录导入监听器
 */
@Slf4j
public class ThreeCatalogueImportListener extends AnalysisEventListener<ThreeCatalogueImportVO> {

    /**
     * 批量处理阈值
     */
    private static final int BATCH_COUNT = 100;

    /**
     * 记录处理的数据
     */
    @Getter
    private final List<ThreeCatalogueImportVO> list = new ArrayList<>();

    /**
     * 失败的记录
     */
    @Getter
    private final List<ThreeCatalogueController.BatchImportResult.FailItem> failItems = new ArrayList<>();

    /**
     * 失败行号列表
     */
    @Getter
    private final List<Integer> failRowNums = new ArrayList<>();

    /**
     * MongoDB操作模板
     */
    private final MongoTemplate mongoTemplate;

    /**
     * 成功导入的数量
     */
    @Getter
    private int successCount = 0;

    /**
     * 当前处理的行号
     */
    private int currentRowIndex = 0;

    /**
     * 原始Excel文件
     */
    @Getter
    @Setter
    private File originalFile;

    /**
     * 处理后的结果文件
     */
    @Getter
    private File resultFile;

    /**
     * 所有数据（包括原始数据和处理后的数据）
     */
    @Getter
    private final List<ThreeCatalogueImportVO> allData = new ArrayList<>();

    public ThreeCatalogueImportListener(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public void invoke(ThreeCatalogueImportVO data, AnalysisContext context) {
        currentRowIndex = context.readRowHolder().getRowIndex() + 1;
        list.add(data);
        allData.add(data); // 保存所有数据，用于后续回填

        // 达到BATCH_COUNT，需要批量处理一次数据，防止数据过多导致内存溢出
        if (list.size() >= BATCH_COUNT) {
            processData();
            list.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 处理剩余数据
        processData();
        list.clear();
    }

    /**
     * 处理导入数据
     */
    private void processData() {
        for (ThreeCatalogueImportVO importVO : list) {
            try {
                // 验证必填字段
                validateRequiredFields(importVO);

                // 解析数据
                ThreeCatalogue threeCatalogue = parseThreeCatalogue(importVO);
                PriceLimit priceLimit = parsePriceLimit(importVO);

                // 检查日期有效性
                if (threeCatalogue.getStartDate() != null && threeCatalogue.getEndDate() != null
                    && threeCatalogue.getStartDate().after(threeCatalogue.getEndDate())) {
                    throw new IllegalArgumentException("开始日期不能晚于结束日期");
                }

                // 查找或创建三目录记录
                ThreeCatalogue existingCatalogue = findOrCreateThreeCatalogue(threeCatalogue);

                // 如果有限价信息，创建限价记录
                if (priceLimit != null) {
                    priceLimit.setThreeCatalogueId(existingCatalogue.getId());
                    priceLimit.setProjectName(existingCatalogue.getProjectName());

                    // 设置时间
                    Date now = new Date();
                    priceLimit.setCreateTime(now);
                    priceLimit.setUpdateTime(now);
                    priceLimit.setEffective(true); // 默认有效

                    mongoTemplate.save(priceLimit);
                }

                successCount++;

            } catch (Exception e) {
                // 记录失败信息
                String errorMsg = e.getMessage();
                importVO.setErrorMsg(errorMsg);
                ThreeCatalogueController.BatchImportResult.FailItem failItem = new ThreeCatalogueController.BatchImportResult.FailItem();
                failItem.setRowNum(currentRowIndex);
                failItem.setReason(errorMsg);
                failItems.add(failItem);
                failRowNums.add(currentRowIndex);
                log.error("导入第{}行数据失败: {}", currentRowIndex, errorMsg);
            }
        }
    }

    /**
     * 验证必填字段
     */
    private void validateRequiredFields(ThreeCatalogueImportVO importVO) {
        List<String> errors = new ArrayList<>();

        if (StrUtil.isBlank(importVO.getProjectName())) {
            errors.add("项目名称不能为空");
        }

        if (StrUtil.isBlank(importVO.getLevel())) {
            errors.add("费用等级不能为空");
        } else if (!Arrays.asList("甲", "乙", "丙").contains(importVO.getLevel().trim())) {
            errors.add("费用等级必须为甲/乙/丙");
        }

        if (StrUtil.isBlank(importVO.getType())) {
            errors.add("目录类别不能为空");
        } else if (!Arrays.asList("药品目录", "诊疗服务", "医用耗材").contains(importVO.getType().trim())) {
            errors.add("目录类别必须为药品目录、诊疗服务、医用耗材");
        }

        if (!errors.isEmpty()) {
            throw new IllegalArgumentException(String.join("；", errors));
        }
    }

    /**
     * 解析三目录数据
     */
    private ThreeCatalogue parseThreeCatalogue(ThreeCatalogueImportVO importVO) {
        ThreeCatalogue catalogue = new ThreeCatalogue();

        catalogue.setSn(StrUtil.isNotBlank(importVO.getSn()) ? importVO.getSn().trim() : null);
        catalogue.setProjectName(importVO.getProjectName().trim());
        catalogue.setLevel(importVO.getLevel().trim());
        catalogue.setType(importVO.getType().trim());

        // 解析日期
        if (StrUtil.isNotBlank(importVO.getStartDate())) {
            try {
                catalogue.setStartDate(DateUtil.parse(importVO.getStartDate().trim()));
            } catch (Exception e) {
                throw new IllegalArgumentException("开始日期格式错误");
            }
        }

        if (StrUtil.isNotBlank(importVO.getEndDate())) {
            try {
                catalogue.setEndDate(DateUtil.parse(importVO.getEndDate().trim()));
            } catch (Exception e) {
                throw new IllegalArgumentException("结束日期格式错误");
            }
        }

        return catalogue;
    }

    /**
     * 解析限价数据
     */
    private PriceLimit parsePriceLimit(ThreeCatalogueImportVO importVO) {
        // 如果没有限价信息，返回null
        if (StrUtil.isBlank(importVO.getPriceLimitLevel()) && StrUtil.isBlank(importVO.getMaxPrice())) {
            return null;
        }

        PriceLimit priceLimit = new PriceLimit();
        priceLimit.setId(SnGeneratorUtil.getId());

        if (StrUtil.isNotBlank(importVO.getPriceLimitLevel())) {
            priceLimit.setPriceLimitLevel(importVO.getPriceLimitLevel().trim());
        }

        // 处理maxPrice字段，从字符串转换为BigDecimal并进行校验
        if (StrUtil.isNotBlank(importVO.getMaxPrice())) {
            String maxPriceStr = importVO.getMaxPrice().trim();
            try {
                // 尝试转换为BigDecimal
                BigDecimal maxPrice = new BigDecimal(maxPriceStr);

                // 校验是否为正数
                if (maxPrice.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new IllegalArgumentException("定价上限金额必须大于0");
                }

                // 校验小数位数是否不超过2位
                if (maxPrice.scale() > 2) {
                    throw new IllegalArgumentException("定价上限金额最多支持2位小数");
                }

                // 设置转换后的值
                priceLimit.setMaxPrice(maxPrice);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("定价上限金额格式不正确，请输入有效的数字");
            }
        }

        return priceLimit;
    }

    /**
     * 查找或创建三目录记录
     * 根据目录编码、项目名称、费用等级、目录类别、开始日期、结束日期这6个字段均相同判断唯一性
     */
    private ThreeCatalogue findOrCreateThreeCatalogue(ThreeCatalogue catalogue) {
        Query query = new Query();

        // 构建查询条件 - 6个字段均相同才认为是同一条记录
        // 1. 目录编码 - 需要考虑null值的情况
        if (StrUtil.isNotBlank(catalogue.getSn())) {
            query.addCriteria(Criteria.where("sn").is(catalogue.getSn()));
        } else {
            query.addCriteria(Criteria.where("sn").is(null));
        }

        // 2. 项目名称 - 必填字段
        query.addCriteria(Criteria.where("projectName").is(catalogue.getProjectName()));

        // 3. 费用等级 - 必填字段
        query.addCriteria(Criteria.where("level").is(catalogue.getLevel()));

        // 4. 目录类别 - 必填字段
        query.addCriteria(Criteria.where("type").is(catalogue.getType()));

        // 5. 开始日期 - 需要考虑null值的情况
        if (catalogue.getStartDate() != null) {
            query.addCriteria(Criteria.where("startDate").is(catalogue.getStartDate()));
        } else {
            query.addCriteria(Criteria.where("startDate").is(null));
        }

        // 6. 结束日期 - 需要考虑null值的情况
        if (catalogue.getEndDate() != null) {
            query.addCriteria(Criteria.where("endDate").is(catalogue.getEndDate()));
        } else {
            query.addCriteria(Criteria.where("endDate").is(null));
        }

        ThreeCatalogue existing = mongoTemplate.findOne(query, ThreeCatalogue.class);

        if (existing != null) {
            return existing;
        }

        // 创建新记录前再次进行唯一性校验，防止并发情况下的重复创建
        ThreeCatalogue duplicateCheck = mongoTemplate.findOne(query, ThreeCatalogue.class);
        if (duplicateCheck != null) {
            return duplicateCheck;
        }

        // 创建新记录
        catalogue.setId(SnGeneratorUtil.getId());
        Date now = new Date();
        catalogue.setCreateTime(now);
        catalogue.setUpdateTime(now);

        try {
            mongoTemplate.save(catalogue);
            log.info("成功创建新三目录记录，ID: {}, 项目名称: {}", catalogue.getId(), catalogue.getProjectName());
            return catalogue;
        } catch (Exception e) {
            // 如果保存失败（可能是并发导致的重复），再次查询
            log.warn("保存三目录记录失败，尝试重新查询: {}", e.getMessage());
            ThreeCatalogue retryFind = mongoTemplate.findOne(query, ThreeCatalogue.class);
            if (retryFind != null) {
                return retryFind;
            }
            // 如果还是找不到，抛出异常
            throw new RuntimeException("创建三目录记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成导入结果Excel文件，包含错误信息
     */
    public File generateResultFile() throws IOException {
        if (originalFile == null) {
            throw new IllegalStateException("原始文件不能为空");
        }

        // 处理错误信息
        for (ThreeCatalogueImportVO vo : allData) {
            Integer rowIndex = allData.indexOf(vo) + 4; // 加3是因为Excel的头部有3行，加1是因为行号从1开始

            // 设置错误信息
            for (ThreeCatalogueController.BatchImportResult.FailItem failItem : failItems) {
                if (Objects.equals(failItem.getRowNum(), rowIndex)) {
                    vo.setErrorMsg(failItem.getReason());
                    break;
                }
            }
        }

        // 创建新的Excel文件
        File resultFile = FileUtil.createTempFile("import_result_", ".xlsx", true);

        try {
            // 使用EasyExcel写入数据
            try (cn.idev.excel.ExcelWriter excelWriter = EasyExcel.write(FileUtil.getOutputStream(resultFile), ThreeCatalogueImportVO.class)
                    .needHead(true)
                    .build()) {
                cn.idev.excel.write.metadata.WriteSheet writeSheet = EasyExcel.writerSheet().build();
                excelWriter.write(allData, writeSheet);
            }
        } catch (Exception e) {
            log.error("生成结果文件失败：", e);
        }

        return resultFile;
    }
}
