# ThreeCatalogue API 使用示例

## 概述

本文档提供了使用新增的"是否有效"字段的API调用示例。

## API 接口

### 查询三目录列表

**接口地址**: `GET /v2/catalogue/list`

**新增参数**:
- `effective`: Boolean类型，可选参数
  - `true`: 只返回有效的目录项
  - `false`: 只返回无效的目录项
  - 不传或`null`: 返回全部目录项

## 使用示例

### 1. 查询所有有效的目录项

```http
GET /v2/catalogue/list?effective=true
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "sn": "A001",
      "projectName": "阿司匹林",
      "level": "甲",
      "type": "药品",
      "startDate": "2024-01-01 00:00:00",
      "endDate": null,
      "effective": true,
      "createTime": "2024-01-01 10:00:00",
      "updateTime": "2024-01-01 10:00:00"
    },
    {
      "id": 2,
      "sn": "B002",
      "projectName": "血常规检查",
      "level": "乙",
      "type": "诊疗",
      "startDate": null,
      "endDate": null,
      "effective": true,
      "createTime": "2024-01-01 10:00:00",
      "updateTime": "2024-01-01 10:00:00"
    }
  ],
  "total": 2,
  "pageNum": 1,
  "pageSize": 10
}
```

### 2. 查询所有无效的目录项

```http
GET /v2/catalogue/list?effective=false
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 3,
      "sn": "C003",
      "projectName": "过期药品",
      "level": "丙",
      "type": "药品",
      "startDate": "2023-01-01 00:00:00",
      "endDate": "2023-12-31 00:00:00",
      "effective": false,
      "createTime": "2023-01-01 10:00:00",
      "updateTime": "2023-01-01 10:00:00"
    }
  ],
  "total": 1,
  "pageNum": 1,
  "pageSize": 10
}
```

### 3. 组合查询：有效的甲类药品

```http
GET /v2/catalogue/list?type=药品&level=甲&effective=true
```

### 4. 分页查询有效项目

```http
GET /v2/catalogue/list?effective=true&pageNum=1&pageSize=20
```

## 前端JavaScript示例

### 1. 基础查询

```javascript
// 查询所有有效的目录项
async function getEffectiveCatalogues() {
  try {
    const response = await fetch('/v2/catalogue/list?effective=true');
    const result = await response.json();
    
    if (result.success) {
      console.log('有效目录项数量:', result.total);
      result.data.forEach(item => {
        console.log(`${item.projectName}: ${item.effective ? '有效' : '无效'}`);
      });
      return result.data;
    } else {
      console.error('查询失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}

// 查询所有无效的目录项
async function getIneffectiveCatalogues() {
  try {
    const response = await fetch('/v2/catalogue/list?effective=false');
    const result = await response.json();
    
    if (result.success) {
      console.log('无效目录项数量:', result.total);
      return result.data;
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}
```

### 2. 动态筛选

```javascript
// 动态筛选函数
async function filterCatalogues(filters) {
  const params = new URLSearchParams();
  
  // 添加基础筛选条件
  if (filters.type) params.append('type', filters.type);
  if (filters.level) params.append('level', filters.level);
  if (filters.projectName) params.append('projectName', filters.projectName);
  
  // 添加有效性筛选
  if (filters.effective !== undefined) {
    params.append('effective', filters.effective);
  }
  
  // 添加分页参数
  if (filters.pageNum) params.append('pageNum', filters.pageNum);
  if (filters.pageSize) params.append('pageSize', filters.pageSize);
  
  try {
    const response = await fetch(`/v2/catalogue/list?${params.toString()}`);
    const result = await response.json();
    return result;
  } catch (error) {
    console.error('查询失败:', error);
    throw error;
  }
}

// 使用示例
filterCatalogues({
  type: '药品',
  level: '甲',
  effective: true,
  pageNum: 1,
  pageSize: 10
}).then(result => {
  console.log('筛选结果:', result);
});
```

### 3. Vue.js 组件示例

```vue
<template>
  <div class="catalogue-list">
    <!-- 筛选条件 -->
    <div class="filters">
      <select v-model="filters.type">
        <option value="">全部类型</option>
        <option value="药品">药品</option>
        <option value="诊疗">诊疗</option>
        <option value="医疗器械">医疗器械</option>
      </select>
      
      <select v-model="filters.level">
        <option value="">全部等级</option>
        <option value="甲">甲</option>
        <option value="乙">乙</option>
        <option value="丙">丙</option>
      </select>
      
      <select v-model="filters.effective">
        <option :value="undefined">全部状态</option>
        <option :value="true">有效</option>
        <option :value="false">无效</option>
      </select>
      
      <button @click="loadData">查询</button>
    </div>
    
    <!-- 数据列表 -->
    <div class="data-list">
      <table>
        <thead>
          <tr>
            <th>项目名称</th>
            <th>类型</th>
            <th>等级</th>
            <th>开始日期</th>
            <th>结束日期</th>
            <th>有效性</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in catalogues" :key="item.id">
            <td>{{ item.projectName }}</td>
            <td>{{ item.type }}</td>
            <td>{{ item.level }}</td>
            <td>{{ formatDate(item.startDate) }}</td>
            <td>{{ formatDate(item.endDate) }}</td>
            <td>
              <span :class="item.effective ? 'effective' : 'ineffective'">
                {{ item.effective ? '有效' : '无效' }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <!-- 分页 -->
    <div class="pagination">
      <button @click="prevPage" :disabled="pageNum <= 1">上一页</button>
      <span>第 {{ pageNum }} 页，共 {{ totalPages }} 页</span>
      <button @click="nextPage" :disabled="pageNum >= totalPages">下一页</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CatalogueList',
  data() {
    return {
      catalogues: [],
      filters: {
        type: '',
        level: '',
        effective: undefined
      },
      pageNum: 1,
      pageSize: 10,
      total: 0
    };
  },
  computed: {
    totalPages() {
      return Math.ceil(this.total / this.pageSize);
    }
  },
  methods: {
    async loadData() {
      try {
        const params = new URLSearchParams();
        
        if (this.filters.type) params.append('type', this.filters.type);
        if (this.filters.level) params.append('level', this.filters.level);
        if (this.filters.effective !== undefined) {
          params.append('effective', this.filters.effective);
        }
        
        params.append('pageNum', this.pageNum);
        params.append('pageSize', this.pageSize);
        
        const response = await fetch(`/v2/catalogue/list?${params.toString()}`);
        const result = await response.json();
        
        if (result.success) {
          this.catalogues = result.data;
          this.total = result.total;
        } else {
          this.$message.error(result.message);
        }
      } catch (error) {
        this.$message.error('查询失败');
        console.error(error);
      }
    },
    
    formatDate(dateStr) {
      if (!dateStr) return '-';
      return new Date(dateStr).toLocaleDateString();
    },
    
    prevPage() {
      if (this.pageNum > 1) {
        this.pageNum--;
        this.loadData();
      }
    },
    
    nextPage() {
      if (this.pageNum < this.totalPages) {
        this.pageNum++;
        this.loadData();
      }
    }
  },
  
  mounted() {
    this.loadData();
  }
};
</script>

<style scoped>
.effective {
  color: green;
  font-weight: bold;
}

.ineffective {
  color: red;
  font-weight: bold;
}

.filters {
  margin-bottom: 20px;
}

.filters select, .filters button {
  margin-right: 10px;
  padding: 5px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

th {
  background-color: #f2f2f2;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

.pagination button {
  margin: 0 10px;
  padding: 5px 10px;
}
</style>
```

## 注意事项

1. **性能考虑**: 有效性筛选是在查询后进行的，大数据量时可能影响性能
2. **时区问题**: 服务器使用本地时间进行有效性判断
3. **缓存策略**: 建议前端对查询结果进行适当缓存
4. **错误处理**: 请求失败时要有适当的错误处理机制

## 最佳实践

1. **合理使用筛选**: 结合其他筛选条件使用，避免单独使用有效性筛选
2. **分页查询**: 大数据量时使用分页查询
3. **状态显示**: 在界面上清晰显示项目的有效性状态
4. **定期刷新**: 对于长时间运行的页面，定期刷新数据以获取最新的有效性状态
