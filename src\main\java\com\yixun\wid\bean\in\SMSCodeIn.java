package com.yixun.wid.bean.in;

import com.yixun.wid.entity.em.AdministratorType;
import com.yixun.wid.entity.em.CaptchaType;
import com.yixun.wid.entity.em.UserType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SMSCodeIn {

    @ApiModelProperty(value="手机号", required = true)
    private String phone;
//    @ApiModelProperty(value="验证码类型", required = true)
//    private CaptchaType captchaType;

	@ApiModelProperty(value="用户类型")
	private AdministratorType administratorType;

}
