# ThreeCatalogue 有效性字段说明

## 概述

为 `ThreeCatalogue`（社保三目录）实体新增了一个"是否有效"的字段，用于判断当前日期下该目录项是否有效。

## 字段定义

### 字段信息
- **字段名**: `effective`
- **类型**: `Boolean`
- **注解**: `@Transient`（不持久化到数据库，动态计算）
- **说明**: 根据当前日期与项目的开始/结束日期判断有效性

### 有效性判断规则

项目在以下情况下被认为是**有效**的：

1. **当前日期在项目的开始/结束日期之内**（包含边界日期）
2. **开始日期在当前日期之前且无结束日期**（长期有效）
3. **结束日期在当前日期之后且无开始日期**（从开始就有效）
4. **既无开始日期也无结束日期**（永久有效）

项目在以下情况下被认为是**无效**的：

1. **当前日期在开始日期之前**（尚未生效）
2. **当前日期在结束日期之后**（已过期）
3. **开始日期晚于结束日期**（无效的日期范围）

## 实现逻辑

```java
public Boolean getEffective() {
    Date now = new Date();
    
    // 如果开始日期和结束日期都为空，认为永久有效
    if (startDate == null && endDate == null) {
        return true;
    }
    
    // 如果只有开始日期，检查当前日期是否在开始日期之后（包含当天）
    if (startDate != null && endDate == null) {
        return !now.before(startDate);
    }
    
    // 如果只有结束日期，检查当前日期是否在结束日期之前（包含当天）
    if (startDate == null && endDate != null) {
        return !now.after(endDate);
    }
    
    // 如果开始日期和结束日期都存在，检查当前日期是否在范围内（包含边界）
    if (startDate != null && endDate != null) {
        return !now.before(startDate) && !now.after(endDate);
    }
    
    return false;
}
```

## 使用示例

### 1. 实体使用

```java
ThreeCatalogue catalogue = new ThreeCatalogue();
catalogue.setProjectName("阿司匹林");
catalogue.setLevel("甲");
catalogue.setType("药品");
catalogue.setStartDate(new Date()); // 今天开始
// 无结束日期，长期有效

// 获取有效性状态
Boolean isEffective = catalogue.getEffective(); // 返回 true
```

### 2. 接口查询

```http
GET /v2/catalogue/list?effective=true
```

**参数说明**:
- `effective=true`: 只返回有效的目录项
- `effective=false`: 只返回无效的目录项
- `effective` 不传或为 `null`: 返回全部目录项

### 3. 前端JavaScript示例

```javascript
// 查询有效的药品目录
fetch('/v2/catalogue/list?type=药品&effective=true')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      data.data.forEach(item => {
        console.log(`${item.projectName}: ${item.effective ? '有效' : '无效'}`);
      });
    }
  });
```

## 业务场景

### 1. 药品目录管理
- **长期有效药品**: 设置开始日期，不设置结束日期
- **临时药品**: 设置开始和结束日期
- **未来上市药品**: 设置未来的开始日期

### 2. 诊疗项目管理
- **常规诊疗项目**: 永久有效（不设置开始和结束日期）
- **季节性项目**: 设置特定的有效期范围
- **试点项目**: 设置明确的试点期间

### 3. 医疗器械管理
- **标准器械**: 长期有效
- **试用器械**: 设置试用期限
- **淘汰器械**: 设置淘汰日期

## 测试用例

### 基础功能测试
- ✅ 既无开始日期也无结束日期（永久有效）
- ✅ 只有开始日期（过去、今天、未来）
- ✅ 只有结束日期（过去、今天、未来）
- ✅ 同时有开始和结束日期（各种组合）

### 边界条件测试
- ✅ 开始日期等于当前日期
- ✅ 结束日期等于当前日期
- ✅ 开始日期和结束日期相同
- ✅ 开始日期晚于结束日期（异常情况）

### 业务场景测试
- ✅ 长期有效的药品目录
- ✅ 临时医疗项目
- ✅ 已过期的医疗器械
- ✅ 未来生效的新药品

### 筛选功能测试
- ✅ 筛选有效项目
- ✅ 筛选无效项目
- ✅ 不筛选（返回全部）
- ✅ 组合筛选（类型+有效性）

## 注意事项

1. **动态计算**: `effective` 字段是动态计算的，不存储在数据库中
2. **时间精度**: 比较基于日期，不考虑具体时间（时分秒）
3. **边界包含**: 开始日期和结束日期都包含在有效期内
4. **性能考虑**: 筛选功能在查询后进行，大数据量时可能影响性能
5. **时区问题**: 使用服务器本地时间进行比较

## 兼容性

- **向后兼容**: 新增字段不影响现有功能
- **API兼容**: 现有接口保持不变，新增可选的 `effective` 参数
- **数据兼容**: 不改变数据库结构，不影响现有数据

## 版本信息

- **引入版本**: 2025-07-22
- **相关文件**:
  - `ThreeCatalogue.java` - 实体类
  - `ThreeCatalogueController.java` - 控制器
  - `ThreeCatalogueEffectiveTest.java` - 单元测试
  - `ThreeCatalogueEffectiveFilterTest.java` - 筛选测试

## 未来扩展

1. **数据库索引**: 如果需要高性能筛选，可考虑在数据库层面实现
2. **缓存机制**: 对于频繁查询的有效性状态，可考虑添加缓存
3. **批量操作**: 提供批量更新有效期的接口
4. **通知机制**: 在项目即将过期或生效时发送通知
