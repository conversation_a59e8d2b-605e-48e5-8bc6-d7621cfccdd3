//package com.yixun.wid.v2.controller;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.yixun.wid.utils.SnGeneratorUtil;
//import com.yixun.wid.v2.config.TestConfig;
//import com.yixun.wid.v2.entity.MedicalInstitutions;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.context.annotation.Import;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.http.MediaType;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.web.servlet.MockMvc;
//import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.Date;
//import java.util.List;
//
//import static org.hamcrest.Matchers.*;
//import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
//
//@SpringBootTest
//@AutoConfigureMockMvc
//@ActiveProfiles("test")
//@Import(TestConfig.class)
//public class MedicalInstitutionsControllerTest {
//
//    @Autowired
//    private MockMvc mockMvc;
//
//    @Autowired
//    private MongoTemplate mongoTemplate;
//
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    private MedicalInstitutions testInstitution;
//
//    @BeforeEach
//    public void setup() {
//        // 清理测试数据
//        mongoTemplate.remove(Query.query(Criteria.where("name").regex("^测试医疗机构")), MedicalInstitutions.class);
//
//        // 创建测试数据
//        testInstitution = new MedicalInstitutions();
//        testInstitution.setId(SnGeneratorUtil.getId());
//        testInstitution.setName("测试医疗机构");
//        testInstitution.setType(Arrays.asList("综合医院", "三级甲等"));
//        testInstitution.setProvince("广东省");
//        testInstitution.setAddress("广州市天河区测试路123号");
//        testInstitution.setAlias("测试医院");
//        testInstitution.setStartDate(new Date());
//        testInstitution.setEndDate(new Date(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000)); // 一年后
//        testInstitution.setHospitalLevel(Arrays.asList("三级", "甲等"));
//        testInstitution.setRegion(Arrays.asList("广东省", "广州市", "天河区"));
//        testInstitution.setLongitude(113.321234);
//        testInstitution.setLatitude(23.123456);
//        testInstitution.setIsAgreementHospital(true);
//        testInstitution.setEnabled(true);
//        testInstitution.setCreateTime(new Date());
//        testInstitution.setUpdateTime(new Date());
//
//        mongoTemplate.save(testInstitution);
//    }
//
//    @Test
//    public void testGetMedicalInstitutionsList() throws Exception {
//        mockMvc.perform(MockMvcRequestBuilders.get("/v2/medical/institutions/list")
//                .param("name", "测试医疗机构")
//                .param("isAgreementHospital", "true")
//                .param("pageNum", "1")
//                .param("pageSize", "10"))
//                .andDo(print())
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data").isArray())
//                .andExpect(jsonPath("$.data", hasSize(greaterThanOrEqualTo(1))))
//                .andExpect(jsonPath("$.data[0].name").value("测试医疗机构"))
//                .andExpect(jsonPath("$.data[0].isAgreementHospital").value(true));
//    }
//
//    @Test
//    public void testGetMedicalInstitutionById() throws Exception {
//        mockMvc.perform(MockMvcRequestBuilders.get("/v2/medical/institutions/detail")
//                .param("id", testInstitution.getId().toString()))
//                .andDo(print())
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data.id").value(testInstitution.getId()))
//                .andExpect(jsonPath("$.data.name").value("测试医疗机构"))
//                .andExpect(jsonPath("$.data.isAgreementHospital").value(true));
//    }
//
//    @Test
//    public void testSaveMedicalInstitution() throws Exception {
//        MedicalInstitutions newInstitution = new MedicalInstitutions();
//        newInstitution.setName("测试医疗机构2");
//        newInstitution.setType(Arrays.asList("综合医院", "二级甲等"));
//        newInstitution.setProvince("广东省");
//        newInstitution.setAddress("广州市海珠区测试路456号");
//        newInstitution.setAlias("测试医院2");
//        newInstitution.setStartDate(new Date());
//        newInstitution.setEndDate(new Date(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000)); // 一年后
//        newInstitution.setHospitalLevel(Arrays.asList("二级", "甲等"));
//        newInstitution.setRegion(Arrays.asList("广东省", "广州市", "海珠区"));
//        newInstitution.setLongitude(113.321234);
//        newInstitution.setLatitude(23.123456);
//        newInstitution.setIsAgreementHospital(false);
//        newInstitution.setEnabled(true);
//
//        mockMvc.perform(MockMvcRequestBuilders.post("/v2/medical/institutions/save")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(newInstitution)))
//                .andDo(print())
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.message").value("操作成功"));
//
//        // 验证数据是否保存成功
//        List<MedicalInstitutions> saved = mongoTemplate.find(
//                Query.query(Criteria.where("name").is("测试医疗机构2")),
//                MedicalInstitutions.class
//        );
//
//        assert !saved.isEmpty();
//        assert "测试医疗机构2".equals(saved.get(0).getName());
//        assert saved.get(0).getId() != null;
//    }
//
//    @Test
//    public void testUpdateMedicalInstitution() throws Exception {
//        // 更新测试机构
//        testInstitution.setName("测试医疗机构-更新");
//        testInstitution.setIsAgreementHospital(false);
//
//        mockMvc.perform(MockMvcRequestBuilders.post("/v2/medical/institutions/update")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(testInstitution)))
//                .andDo(print())
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.message").value("操作成功"));
//
//        // 验证数据是否更新成功
//        MedicalInstitutions updated = mongoTemplate.findById(testInstitution.getId(), MedicalInstitutions.class);
//        assert updated != null;
//        assert "测试医疗机构-更新".equals(updated.getName());
//        assert !updated.getIsAgreementHospital();
//    }
//
//    @Test
//    public void testBatchDeleteMedicalInstitutions() throws Exception {
//        // 创建要删除的测试数据
//        List<MedicalInstitutions> toDelete = new ArrayList<>();
//        for (int i = 0; i < 3; i++) {
//            MedicalInstitutions institution = new MedicalInstitutions();
//            institution.setId(SnGeneratorUtil.getId());
//            institution.setName("测试医疗机构-删除" + i);
//            institution.setCreateTime(new Date());
//            institution.setUpdateTime(new Date());
//            toDelete.add(institution);
//        }
//        mongoTemplate.insertAll(toDelete);
//
//        // 提取ID列表
//        List<Long> ids = new ArrayList<>();
//        for (MedicalInstitutions institution : toDelete) {
//            ids.add(institution.getId());
//        }
//
//        mockMvc.perform(MockMvcRequestBuilders.post("/v2/medical/institutions/batchDelete")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(ids)))
//                .andDo(print())
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.message").value("删除成功"));
//
//        // 验证数据是否删除成功
//        for (Long id : ids) {
//            MedicalInstitutions deleted = mongoTemplate.findById(id, MedicalInstitutions.class);
//            assert deleted == null;
//        }
//    }
//}
