package com.yixun.wid.v2.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
public class SimilarAiCheckOut {

    @ApiModelProperty("搜索的医疗机构名称")
    @JSONField(name = "search_name")
    private String searchName;

    @ApiModelProperty("处理后的机构名称")
    @JSONField(name = "process_name")
    private String processName;

    @ApiModelProperty("相似医疗机构列表")
    private List<SimilarAiCheckResult> result;
}
