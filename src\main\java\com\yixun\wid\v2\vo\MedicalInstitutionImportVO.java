package com.yixun.wid.v2.vo;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 医疗机构导入实体类
 */
@Data
public class MedicalInstitutionImportVO {

    /**
     * 机构名称
     */
    @ExcelProperty(value = "*机构名称", index = 0)
    private String name;

    /**
     * 机构别称 (存在多个时，用英文;分隔) +
     */
    @ExcelProperty(value = "机构别称", index = 2)
    private String aliases;

    /**
     * 是否协议机构
     */
    @ExcelProperty(value = "*是否协议机构", index = 1)
    private String isAgreementHospital;

    /**
     * 机构等级 (一级选项：一级;二级;三级;未定级；二级选项：甲等;乙等;丙等)
     */
    @ExcelProperty(value = "机构等级", index = 3)
    private String hospitalLevel;

	@ExcelProperty(index = 4)
	private String level2;

    /**
     * 机构类型 (多个的，可用英文;分隔)
     */
    @ExcelProperty(value = "机构类型", index = 7)
    private String type;

    /**
     * 开始日期 (年/月/日格式)
     */
    @ExcelProperty(value = "开始日期", index = 5)
    private String startDate;

    /**
     * 结束日期 (年/月/日格式)
     */
    @ExcelProperty(value = "结束日期", index = 6)
    private String endDate;

    /**
     * 省市区 (按省/市/区格式填充)
     */
    @ExcelProperty(value = "省市区", index = 10)
    private String region;

    /**
     * 详细地址
     */
    @ExcelProperty(value = "详细地址", index = 9)
    private String address;

    /**
     * 经纬度 (经度,维度)
     */
    @ExcelProperty(value = "经纬度",index = 11)
    private String longitudeLatitude;

    /**
     * 限价医院等级
     */
    @ExcelProperty(value = "限价医院等级", index = 8)
    private String priceLimitLevel;

    /**
     * 错误信息/相似机构
     */
    @ExcelProperty(value = "错误信息/相似机构", index = 12)
    private String errorMsg;

    /**
     * 行号（用于错误定位，不参与Excel导入导出）
     */
    private Integer rowIndex;
}
