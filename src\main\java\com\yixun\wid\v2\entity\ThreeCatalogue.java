package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 社保三目录
 */
@Data
public class ThreeCatalogue {

	/**
	 * 主键id
	 */
	@Id
	private Long id;

	/**
	 * 目录编码 可重复
	 */
	private String sn;

	/**
	 * 目录类别
	 */
	@NotNull(message = "目录类别不能为空")
	private String type;

	/**
	 * 项目名称
	 */
	@NotNull(message = "项目名称不能为空")
	private String projectName;

	/**
	 * 费用等级 甲乙丙
	 */
	@NotNull(message = "费用等级不能为空")
	private String level;

	/**
	 * 开始日期
	 */
	@JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date startDate;

	/**
	 * 结束日期
	 */
	@JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date endDate;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	@DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date updateTime;

	/**
	 * 是否有效
	 * 根据当前日期与开始/结束日期判断有效性：
	 * 1. 当前日期在开始日期之后且在结束日期之前（包含边界）
	 * 2. 开始日期在当前日期之前且无结束日期
	 * 3. 结束日期在当前日期之后且无开始日期
	 * 4. 既无开始日期也无结束日期（永久有效）
	 */
	@Transient
	private Boolean effective;

	/**
	 * 获取是否有效状态
	 * @return true表示有效，false表示无效
	 */
	public Boolean getEffective() {
		Date now = new Date();

		// 如果开始日期和结束日期都为空，认为永久有效
		if (startDate == null && endDate == null) {
			return true;
		}

		// 如果只有开始日期，检查当前日期是否在开始日期之后（包含当天）
		if (startDate != null && endDate == null) {
			return !now.before(startDate);
		}

		// 如果只有结束日期，检查当前日期是否在结束日期之前（包含当天）
		if (startDate == null && endDate != null) {
			return !now.after(endDate);
		}

		// 如果开始日期和结束日期都存在，检查当前日期是否在范围内（包含边界）
		if (startDate != null && endDate != null) {
			return !now.before(startDate) && !now.after(endDate);
		}

		return false;
	}

}
