package com.yixun.wid.v2.vo.ai;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.List;

/**
 * 相似查找请求参数
 */
@Data
public class QuerySimilarityRequest {

    /**
     * 输入查询列表
     */
    private List<String> querys;

    /**
     * 目标对比列表
     */
    private List<String> targets;

    /**
     * 返回前K个结果
     */
    @JSONField(name = "top_k")
    private Integer topK;

    /**
     * 是否重新排序
     */
    private Boolean rerank;
}
