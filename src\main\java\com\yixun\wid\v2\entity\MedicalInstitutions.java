package com.yixun.wid.v2.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 医疗机构
 */
@Data
public class MedicalInstitutions {

    /**
     * 主键id
     */
    @Id
    private Long id;

    /**
     * 机构名称
     */
    private String name;

    /**
     * 机构类型
     */
    private List<String> type;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 机构别名列表
     */
    private List<String> aliases;

    /**
     * 开始日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATE_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date startDate;

    /**
     * 结束日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATE_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date endDate;

    /**
     * 机构等级
     */
    private List<String> hospitalLevel;

    /**
     * 省/市/区
     */
    private List<String> region;

    /**
     * 省/市/区代码
     */
    private List<String> regionCode;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 是否协议机构
     */
    private Boolean isAgreementHospital;

	/**
	 * 限价医院等级
	 */
	private String priceLimitLevel;

    /**
     * 是否为主机构
     */
    private Boolean isMainHospital;

    /**
     * 启用状态
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;

    /**
     * 是否失效
     * 当终止日期为空时，表示一直有效
     * 当当前日期超过终止日期时，自动变为已终止
     */
    @Transient
    private Boolean expired;

    public Boolean getExpired() {
        if (endDate == null) {
            return false; // 终止日期为空，表示一直有效
        }
        return new Date().after(endDate); // 当前日期超过终止日期，已终止
    }
}
