package com.yixun.wid.v2.controller;

import com.yixun.wid.v2.entity.PriceLimit;
import com.yixun.wid.v2.entity.ThreeCatalogue;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * PriceLimitController有效性测试类
 * 测试PriceLimitController中的effective状态赋值逻辑
 */
public class PriceLimitControllerEffectiveTest {

    /**
     * 获取指定天数前/后的日期
     * @param days 天数，正数表示未来，负数表示过去
     * @return 日期
     */
    private Date getDateByDays(int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return calendar.getTime();
    }

    /**
     * 创建测试用的ThreeCatalogue对象
     * @param effective 是否有效
     * @return ThreeCatalogue对象
     */
    private ThreeCatalogue createTestThreeCatalogue(boolean effective) {
        ThreeCatalogue catalogue = new ThreeCatalogue();
        catalogue.setId(12345L);
        catalogue.setProjectName("测试药品");
        catalogue.setLevel("甲");
        catalogue.setType("药品");
        
        if (effective) {
            // 创建有效的三目录（开始日期在过去，无结束日期）
            catalogue.setStartDate(getDateByDays(-30));
            // 无结束日期，长期有效
        } else {
            // 创建无效的三目录（已过期）
            catalogue.setStartDate(getDateByDays(-60));
            catalogue.setEndDate(getDateByDays(-10));
        }
        
        return catalogue;
    }

    /**
     * 创建测试用的PriceLimit对象
     * @param threeCatalogueId 关联的三目录ID
     * @param hospitalLevel 医院等级
     * @param maxPrice 最高价格
     * @return PriceLimit对象
     */
    private PriceLimit createTestPriceLimit(Long threeCatalogueId, String hospitalLevel, BigDecimal maxPrice) {
        PriceLimit priceLimit = new PriceLimit();
        priceLimit.setId(System.currentTimeMillis()); // 使用时间戳作为ID
        priceLimit.setThreeCatalogueId(threeCatalogueId);
        priceLimit.setPriceLimitLevel(hospitalLevel);
        priceLimit.setMaxPrice(maxPrice);
        priceLimit.setCreateTime(new Date());
        priceLimit.setUpdateTime(new Date());
        return priceLimit;
    }

    @Test
    public void testAttachThreeCatalogueInfoWithEffectiveTrue() {
        // 测试：附加三目录信息并设置有效性状态（有效情况）
        
        // 创建有效的三目录
        ThreeCatalogue threeCatalogue = createTestThreeCatalogue(true);
        assert threeCatalogue.getEffective() == true;
        
        // 创建PriceLimit列表
        List<PriceLimit> priceLimits = new ArrayList<>();
        priceLimits.add(createTestPriceLimit(threeCatalogue.getId(), "三甲", new BigDecimal("100.00")));
        priceLimits.add(createTestPriceLimit(threeCatalogue.getId(), "三乙", new BigDecimal("80.00")));
        priceLimits.add(createTestPriceLimit(threeCatalogue.getId(), "二甲", new BigDecimal("60.00")));
        
        // 模拟Controller中的逻辑：附加三目录信息并设置有效性状态
        for (PriceLimit priceLimit : priceLimits) {
            if (priceLimit.getThreeCatalogueId() != null) {
                // 模拟从数据库查询ThreeCatalogue
                if (priceLimit.getThreeCatalogueId().equals(threeCatalogue.getId())) {
                    if (threeCatalogue.getProjectName() != null && !threeCatalogue.getProjectName().isEmpty()) {
                        priceLimit.setProjectName(threeCatalogue.getProjectName());
                    }
                    // 为PriceLimit的effective状态赋值为ThreeCatalogue的effective字段的值
                    priceLimit.setEffective(threeCatalogue.getEffective());
                }
            }
        }
        
        // 验证结果
        for (PriceLimit priceLimit : priceLimits) {
            assert priceLimit.getProjectName().equals("测试药品");
            assert priceLimit.getEffective() == true;
        }
        
        System.out.println("测试通过：附加三目录信息并设置有效性状态（有效情况）");
        System.out.println("  三目录: " + threeCatalogue.getProjectName() + " (有效性: " + threeCatalogue.getEffective() + ")");
        for (PriceLimit priceLimit : priceLimits) {
            System.out.println("    " + priceLimit.getPriceLimitLevel() + ": " + priceLimit.getMaxPrice() + "元 (有效性: " + priceLimit.getEffective() + ")");
        }
    }

    @Test
    public void testAttachThreeCatalogueInfoWithEffectiveFalse() {
        // 测试：附加三目录信息并设置有效性状态（无效情况）
        
        // 创建无效的三目录
        ThreeCatalogue threeCatalogue = createTestThreeCatalogue(false);
        assert threeCatalogue.getEffective() == false;
        
        // 创建PriceLimit列表
        List<PriceLimit> priceLimits = new ArrayList<>();
        priceLimits.add(createTestPriceLimit(threeCatalogue.getId(), "三甲", new BigDecimal("100.00")));
        priceLimits.add(createTestPriceLimit(threeCatalogue.getId(), "三乙", new BigDecimal("80.00")));
        
        // 模拟Controller中的逻辑：附加三目录信息并设置有效性状态
        for (PriceLimit priceLimit : priceLimits) {
            if (priceLimit.getThreeCatalogueId() != null) {
                // 模拟从数据库查询ThreeCatalogue
                if (priceLimit.getThreeCatalogueId().equals(threeCatalogue.getId())) {
                    if (threeCatalogue.getProjectName() != null && !threeCatalogue.getProjectName().isEmpty()) {
                        priceLimit.setProjectName(threeCatalogue.getProjectName());
                    }
                    // 为PriceLimit的effective状态赋值为ThreeCatalogue的effective字段的值
                    priceLimit.setEffective(threeCatalogue.getEffective());
                }
            }
        }
        
        // 验证结果
        for (PriceLimit priceLimit : priceLimits) {
            assert priceLimit.getProjectName().equals("测试药品");
            assert priceLimit.getEffective() == false;
        }
        
        System.out.println("测试通过：附加三目录信息并设置有效性状态（无效情况）");
        System.out.println("  三目录: " + threeCatalogue.getProjectName() + " (有效性: " + threeCatalogue.getEffective() + ")");
        for (PriceLimit priceLimit : priceLimits) {
            System.out.println("    " + priceLimit.getPriceLimitLevel() + ": " + priceLimit.getMaxPrice() + "元 (有效性: " + priceLimit.getEffective() + ")");
        }
    }

    @Test
    public void testSinglePriceLimitDetail() {
        // 测试：单个PriceLimit详情查询的有效性设置
        
        // 创建有效的三目录
        ThreeCatalogue threeCatalogue = createTestThreeCatalogue(true);
        assert threeCatalogue.getEffective() == true;
        
        // 创建单个PriceLimit
        PriceLimit priceLimit = createTestPriceLimit(threeCatalogue.getId(), "三甲", new BigDecimal("150.00"));
        
        // 模拟getDetail方法中的逻辑
        if (priceLimit.getThreeCatalogueId() != null) {
            // 模拟从数据库查询ThreeCatalogue
            if (priceLimit.getThreeCatalogueId().equals(threeCatalogue.getId())) {
                if (threeCatalogue.getProjectName() != null && !threeCatalogue.getProjectName().isEmpty()) {
                    priceLimit.setProjectName(threeCatalogue.getProjectName());
                }
                // 为PriceLimit的effective状态赋值为ThreeCatalogue的effective字段的值
                priceLimit.setEffective(threeCatalogue.getEffective());
            }
        }
        
        // 验证结果
        assert priceLimit.getProjectName().equals("测试药品");
        assert priceLimit.getEffective() == true;
        assert priceLimit.getPriceLimitLevel().equals("三甲");
        assert priceLimit.getMaxPrice().compareTo(new BigDecimal("150.00")) == 0;
        
        System.out.println("测试通过：单个PriceLimit详情查询的有效性设置");
        System.out.println("  项目名称: " + priceLimit.getProjectName());
        System.out.println("  医院等级: " + priceLimit.getPriceLimitLevel());
        System.out.println("  最高价格: " + priceLimit.getMaxPrice() + "元");
        System.out.println("  有效性: " + priceLimit.getEffective());
    }

    @Test
    public void testNullThreeCatalogueHandling() {
        // 测试：处理ThreeCatalogueId为null的情况
        
        // 创建没有关联三目录的PriceLimit
        PriceLimit priceLimit = new PriceLimit();
        priceLimit.setId(99999L);
        priceLimit.setThreeCatalogueId(null); // 没有关联三目录
        priceLimit.setPriceLimitLevel("三甲");
        priceLimit.setMaxPrice(new BigDecimal("200.00"));
        
        // 模拟Controller中的逻辑
        if (priceLimit.getThreeCatalogueId() != null) {
            // 这个分支不会执行
            priceLimit.setProjectName("不应该设置");
            priceLimit.setEffective(true);
        }
        
        // 验证结果：没有设置项目名称和有效性
        assert priceLimit.getProjectName() == null;
        assert priceLimit.getEffective() == null;
        assert priceLimit.getPriceLimitLevel().equals("三甲");
        assert priceLimit.getMaxPrice().compareTo(new BigDecimal("200.00")) == 0;
        
        System.out.println("测试通过：处理ThreeCatalogueId为null的情况");
        System.out.println("  项目名称: " + priceLimit.getProjectName() + " (应该为null)");
        System.out.println("  有效性: " + priceLimit.getEffective() + " (应该为null)");
        System.out.println("  医院等级: " + priceLimit.getPriceLimitLevel());
        System.out.println("  最高价格: " + priceLimit.getMaxPrice() + "元");
    }

    @Test
    public void testMixedEffectiveStatus() {
        // 测试：混合有效性状态的情况
        
        // 创建有效和无效的三目录
        ThreeCatalogue validCatalogue = createTestThreeCatalogue(true);
        ThreeCatalogue invalidCatalogue = createTestThreeCatalogue(false);
        invalidCatalogue.setId(54321L); // 设置不同的ID
        invalidCatalogue.setProjectName("过期药品");
        
        // 创建混合的PriceLimit列表
        List<PriceLimit> priceLimits = new ArrayList<>();
        priceLimits.add(createTestPriceLimit(validCatalogue.getId(), "三甲", new BigDecimal("100.00")));
        priceLimits.add(createTestPriceLimit(invalidCatalogue.getId(), "三甲", new BigDecimal("80.00")));
        priceLimits.add(createTestPriceLimit(validCatalogue.getId(), "二甲", new BigDecimal("60.00")));
        
        // 模拟Controller中的逻辑
        for (PriceLimit priceLimit : priceLimits) {
            if (priceLimit.getThreeCatalogueId() != null) {
                ThreeCatalogue threeCatalogue = null;
                
                // 模拟从数据库查询ThreeCatalogue
                if (priceLimit.getThreeCatalogueId().equals(validCatalogue.getId())) {
                    threeCatalogue = validCatalogue;
                } else if (priceLimit.getThreeCatalogueId().equals(invalidCatalogue.getId())) {
                    threeCatalogue = invalidCatalogue;
                }
                
                if (threeCatalogue != null) {
                    if (threeCatalogue.getProjectName() != null && !threeCatalogue.getProjectName().isEmpty()) {
                        priceLimit.setProjectName(threeCatalogue.getProjectName());
                    }
                    // 为PriceLimit的effective状态赋值为ThreeCatalogue的effective字段的值
                    priceLimit.setEffective(threeCatalogue.getEffective());
                }
            }
        }
        
        // 验证结果
        assert priceLimits.get(0).getProjectName().equals("测试药品");
        assert priceLimits.get(0).getEffective() == true;
        
        assert priceLimits.get(1).getProjectName().equals("过期药品");
        assert priceLimits.get(1).getEffective() == false;
        
        assert priceLimits.get(2).getProjectName().equals("测试药品");
        assert priceLimits.get(2).getEffective() == true;
        
        System.out.println("测试通过：混合有效性状态的情况");
        for (int i = 0; i < priceLimits.size(); i++) {
            PriceLimit pl = priceLimits.get(i);
            System.out.println("  " + (i+1) + ". " + pl.getProjectName() + " - " + pl.getPriceLimitLevel() +
                ": " + pl.getMaxPrice() + "元 (有效性: " + pl.getEffective() + ")");
        }
    }
}
