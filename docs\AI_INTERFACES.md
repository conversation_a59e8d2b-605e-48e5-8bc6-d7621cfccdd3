# AI接口文档

本文档描述了工伤待遇管理系统中与AI服务集成的接口。

## 概述

系统集成了多个AI服务接口，用于智能识别和处理工伤待遇相关的材料和信息。所有AI接口都通过 `AiUtils` 工具类调用外部AI服务。

## 接口列表

### 1. 受理信息和临床诊断识别

**接口路径**: `POST /v2/medical/cases/accepted-information-diagnosis`

**功能描述**: 调用AI服务进行受理信息和临床诊断的智能识别，将识别结果以MedicalCases结构形式返回。支持两种调用方式：通过业务ID或直接传入请求对象。**注意：此接口仅返回识别结果，不会自动保存到数据库。**

**请求参数**（二选一）:
```
方式1 - 通过ID调用:
id (Long): 工伤待遇业务ID

方式2 - 直接传入对象:
request (AcceptedInformationDiagnosisRequest): 请求对象，包含完整的材料信息
```

**请求示例**:

方式1 - 通过ID调用:
```bash
POST /v2/medical/cases/accepted-information-diagnosis?id=123456
```

方式2 - 直接传入对象:
```bash
POST /v2/medical/cases/accepted-information-diagnosis
Content-Type: application/json

{
  "application": {
    "id_card": ["身份证.jpg"]
  },
  "medical": [{
    "visit_type": "住院",
    "visit_date": "2024-01-01",
    "material": {
      "medical_record": {
        "medical_record_subcategory": ["病历子类.pdf"]
      },
      "electronic_invoice": ["电子发票.pdf"]
    }
  }]
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 123456,
    "caseNumber": "BX101420250721001",
    "status": "ACCEPTED",
    "workerName": "张三",
    "organization": "测试公司",
    "idCard": "110101199001011234",
    "gender": "男",
    "accidentDate": "2024-01-01",
    "injuryDiagnoses": ["左手骨折", "软组织挫伤"],
    "hasInsurance": true,
    "insuranceAddress": "北京市",
    "createTime": "2024-07-21 10:00:00",
    "updateTime": "2024-07-21 10:30:00"
    // ... 其他MedicalCases字段
  }
}
```

**说明**:
- 当通过ID调用时，返回包含原有基本信息和AI识别数据的MedicalCases对象
- 当直接传入request对象时，返回仅包含AI识别数据的MedicalCases对象
- **重要**：此接口不会自动保存数据到数据库，仅用于展示识别结果，是否保存由用户决定
- AI识别的数据映射关系：
  - `accidentDate` -> `accidentDate` (事故日期)
  - `employeeName` -> `workerName` (职工姓名)
  - `gender` -> `gender` (性别)
  - `idNumber` -> `idCard` (身份证号)
  - `employerName` -> `organization` (用人单位名称)
  - `clinicalDiagnosis` -> `injuryDiagnoses` (临床诊断列表)

**错误响应**:
- `6001`: 工伤待遇业务不存在（仅方式1）
- `1001`: 该案件尚未进行材料分类，请先进行材料分类（仅方式1）
- `1001`: 参数错误：id和request参数必须提供其中一个
- `1001`: 参数错误：id和request参数只能提供其中一个
- `1001`: 受理信息和临床诊断识别失败：AI服务返回空响应
- `1001`: 受理信息和临床诊断识别失败：status=error/failed/空值等非success状态

**AI状态判断逻辑**:
- 接口会检查AI服务返回的`status`字段
- 只有当`status`为`"success"`时才会继续处理数据
- 其他任何状态（`error`、`failed`、空字符串、`null`等）都会抛出异常并记录错误日志
- 异常信息会包含具体的状态值和错误消息，便于问题排查

### 2. 手术信息识别

**接口路径**: `POST /v2/medical/cases/surgical-information`

**功能描述**: 调用AI服务进行手术信息的智能识别。支持两种调用方式：通过业务ID或直接传入请求对象。

**请求参数**（二选一）:
```
方式1 - 通过ID调用:
id (Long): 工伤待遇业务ID

方式2 - 直接传入对象:
request (AcceptedInformationDiagnosisRequest): 请求对象，包含完整的材料信息
```

**请求示例**:

方式1 - 通过ID调用:
```bash
POST /v2/medical/cases/surgical-information?id=123456
```

方式2 - 直接传入对象:
```bash
POST /v2/medical/cases/surgical-information
Content-Type: application/json

{
  "application": {
    "id_card": ["身份证.jpg"]
  },
  "medical": [{
    "visit_type": "住院",
    "visit_date": "2024-01-01",
    "material": {
      "medical_record": {
        "medical_record_subcategory": ["病历子类.pdf"]
      },
      "surgical_record": ["手术记录.pdf"]
    }
  }]
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "status": "success",
    "message": "手术信息识别成功",
    "data": {
      "surgical_name": ["阑尾切除术", "胆囊切除术"]
    }
  }
}
```

**错误响应**:
- `6001`: 工伤待遇业务不存在（仅方式1）
- `1001`: 该案件尚未进行材料分类，请先进行材料分类（仅方式1）
- `1001`: 参数错误：id和request参数必须提供其中一个
- `1001`: 参数错误：id和request参数只能提供其中一个

### 3. 账单信息识别

**接口路径**: `POST /v2/medical/cases/bill-information`

**功能描述**: 调用AI服务进行账单信息的智能识别。支持两种调用方式：通过业务ID或直接传入请求对象。

**请求参数**（二选一）:
```
方式1 - 通过ID调用:
id (Long): 工伤待遇业务ID

方式2 - 直接传入对象:
request (AcceptedInformationDiagnosisRequest): 请求对象，包含完整的材料信息
```

**请求示例**:

方式1 - 通过ID调用:
```bash
POST /v2/medical/cases/bill-information?id=123456
```

方式2 - 直接传入对象:
```bash
POST /v2/medical/cases/bill-information
Content-Type: application/json

{
  "application": {
    "id_card": ["身份证.jpg"]
  },
  "medical": [{
    "visit_type": "住院",
    "visit_date": "2024-01-01",
    "material": {
      "electronic_invoice": ["电子发票.pdf"],
      "medical_record": {
        "medical_record_subcategory": ["病历子类.pdf"]
      }
    }
  }]
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "status": "success",
    "message": "账单信息识别成功",
    "data": {
      "bill_Information": [{
        "bill_number": "INV20240101001",
        "hospital": "测试医院",
        "treatment_type": "住院",
        "bill_amount": 1500.00,
        "admission_time": "2024-01-01",
        "discharge_time": "2024-01-05",
        "stay_days": 4,
        "expense_details": [{
          "expense_item": "治疗费",
          "amount": 800.00,
          "expense_list": [{
            "name": "手术费",
            "code": "SF001",
            "quantity": 1,
            "unit_price": 800.00,
            "amount": 800.00
          }]
        }]
      }]
    }
  }
}
```

**错误响应**:
- `6001`: 工伤待遇业务不存在（仅方式1）
- `1001`: 该案件尚未进行材料分类，请先进行材料分类（仅方式1）
- `1001`: 参数错误：id和request参数必须提供其中一个
- `1001`: 参数错误：id和request参数只能提供其中一个

## 使用流程

1. **材料分类**: 首先需要调用材料分类接口，对上传的材料进行分类识别
2. **信息识别**: 材料分类完成后，可以调用受理信息和临床诊断识别接口
3. **手术信息识别**: 如果材料中包含手术相关信息，可以调用手术信息识别接口
4. **账单信息识别**: 如果材料中包含账单相关信息，可以调用账单信息识别接口

## 前置条件

- 工伤待遇业务必须已存在于系统中
- 必须已完成材料分类，即 `MedicalCases` 中的 `AcceptedInformationDiagnosisRequest` 字段不能为空

## 技术实现

### 控制器层
- `MedicalCasesController.acceptedInformationDiagnosis()` - 受理信息和临床诊断识别
- `MedicalCasesController.surgicalInformation()` - 手术信息识别
- `MedicalCasesController.billInformation()` - 账单信息识别

### 服务层
- `AiUtils.acceptedInformationDiagnosis()` - 调用AI服务进行受理信息和临床诊断识别
- `AiUtils.surgicalInformation()` - 调用AI服务进行手术信息识别
- `AiUtils.billInformation()` - 调用AI服务进行账单信息识别

### 数据模型
- `AcceptedInformationDiagnosisRequest` - 请求数据结构
- `AcceptedInformationDiagnosisResponse` - 受理信息和临床诊断响应结构
- `AcceptedInformationDiagnosisVO` - 受理信息和临床诊断识别接口返回的VO对象
- `SurgicalInformationResponse` - 手术信息响应结构
- `BillInformationResponse` - 账单信息响应结构

## 注意事项

1. **数据依赖**: 这两个接口都依赖于材料分类的结果，必须先完成材料分类
2. **错误处理**: 接口会返回标准的错误码和错误信息，前端需要根据错误码进行相应处理
3. **日志记录**: 所有AI接口调用都会记录详细的日志，便于问题排查
4. **性能考虑**: AI识别可能需要较长时间，建议前端做好加载状态提示
5. **返回数据结构**: 受理信息和临床诊断识别接口现在返回专用的VO对象，而不是完整的MedicalCases对象，这样可以避免暴露不必要的内部数据结构

## 测试验证

项目中包含了完整的测试用例：
- `ApiStructureTest` - 验证数据结构的正确性
- `AiInterfaceTest` - 验证接口的基本功能

运行测试：
```bash
mvn test -Dtest=AiInterfaceTest
mvn test -Dtest=ApiStructureTest
```

## 更新历史

- **2025-07-22**: 优化受理信息和临床诊断识别接口，现在返回专用的AcceptedInformationDiagnosisVO对象而不是MedicalCases对象
- **2025-07-21**: 新增手术信息识别接口，完善接口文档
