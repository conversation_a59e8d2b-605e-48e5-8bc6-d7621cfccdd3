package com.yixun.wid.v2.processor;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.controller.ThreeCatalogueController;
import com.yixun.wid.v2.entity.PriceLimit;
import com.yixun.wid.v2.entity.ThreeCatalogue;
import com.yixun.wid.v2.vo.ThreeCatalogueImportVO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 三目录批量处理器
 * 复用ThreeCatalogueImportListener的核心逻辑，用于处理前端传入的批量数据
 */
@Slf4j
public class ThreeCatalogueBatchProcessor {

    /**
     * 失败的记录
     */
    @Getter
    private final List<ThreeCatalogueController.BatchImportResult.FailItem> failItems = new ArrayList<>();

    /**
     * MongoDB操作模板
     */
    private final MongoTemplate mongoTemplate;

    /**
     * 成功处理的数量
     */
    @Getter
    private int successCount = 0;

    public ThreeCatalogueBatchProcessor(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    /**
     * 处理单条数据
     *
     * @param importVO 导入数据
     * @param rowNum 行号
     */
    public void processItem(ThreeCatalogueImportVO importVO, int rowNum) {
        try {
            // 验证必填字段
            validateRequiredFields(importVO);

            // 解析数据
            ThreeCatalogue threeCatalogue = parseThreeCatalogue(importVO);
            PriceLimit priceLimit = parsePriceLimit(importVO);

            // 检查日期有效性
            if (threeCatalogue.getStartDate() != null && threeCatalogue.getEndDate() != null
                && threeCatalogue.getStartDate().after(threeCatalogue.getEndDate())) {
                throw new IllegalArgumentException("开始日期不能晚于结束日期");
            }

            // 查找或创建三目录记录
            ThreeCatalogue existingCatalogue = findOrCreateThreeCatalogue(threeCatalogue);

            // 如果有限价信息，创建限价记录
            if (priceLimit != null) {
                priceLimit.setThreeCatalogueId(existingCatalogue.getId());
                priceLimit.setProjectName(existingCatalogue.getProjectName());

                // 设置时间
                Date now = new Date();
                priceLimit.setCreateTime(now);
                priceLimit.setUpdateTime(now);
                priceLimit.setEffective(true); // 默认有效

                mongoTemplate.save(priceLimit);
            }

            successCount++;

        } catch (Exception e) {
            // 记录失败信息
            ThreeCatalogueController.BatchImportResult.FailItem failItem =
                new ThreeCatalogueController.BatchImportResult.FailItem();
            failItem.setRowNum(rowNum);
            failItem.setReason(e.getMessage());

            failItems.add(failItem);

            log.warn("处理第{}行数据失败: {}, 项目名称: {}", rowNum, e.getMessage(), importVO.getProjectName());
        }
    }

    /**
     * 验证必填字段
     */
    private void validateRequiredFields(ThreeCatalogueImportVO importVO) {
        List<String> errors = new ArrayList<>();

        if (StrUtil.isBlank(importVO.getProjectName())) {
            errors.add("项目名称不能为空");
        }

        if (StrUtil.isBlank(importVO.getLevel())) {
            errors.add("费用等级不能为空");
        } else if (!Arrays.asList("甲", "乙", "丙").contains(importVO.getLevel().trim())) {
            errors.add("费用等级必须为甲/乙/丙");
        }

        if (StrUtil.isBlank(importVO.getType())) {
            errors.add("目录类别不能为空");
        } else if (!Arrays.asList("药品目录", "诊疗服务", "医用耗材").contains(importVO.getType().trim())) {
            errors.add("目录类别必须为药品目录、诊疗服务、医用耗材");
        }

        if (!errors.isEmpty()) {
            throw new IllegalArgumentException(String.join("；", errors));
        }
    }

    /**
     * 解析三目录数据
     */
    private ThreeCatalogue parseThreeCatalogue(ThreeCatalogueImportVO importVO) {
        ThreeCatalogue catalogue = new ThreeCatalogue();

        catalogue.setSn(StrUtil.isNotBlank(importVO.getSn()) ? importVO.getSn().trim() : null);
        catalogue.setProjectName(importVO.getProjectName().trim());
        catalogue.setLevel(importVO.getLevel().trim());
        catalogue.setType(importVO.getType().trim());

        // 解析日期
        if (StrUtil.isNotBlank(importVO.getStartDate())) {
            try {
                catalogue.setStartDate(DateUtil.parse(importVO.getStartDate().trim()));
            } catch (Exception e) {
                throw new IllegalArgumentException("开始日期格式错误");
            }
        }

        if (StrUtil.isNotBlank(importVO.getEndDate())) {
            try {
                catalogue.setEndDate(DateUtil.parse(importVO.getEndDate().trim()));
            } catch (Exception e) {
                throw new IllegalArgumentException("结束日期格式错误");
            }
        }

        return catalogue;
    }

    /**
     * 解析限价数据
     */
    private PriceLimit parsePriceLimit(ThreeCatalogueImportVO importVO) {
        // 如果没有限价医院等级，则不创建限价记录
        if (StrUtil.isBlank(importVO.getPriceLimitLevel())) {
            return null;
        }

        PriceLimit priceLimit = new PriceLimit();
        priceLimit.setId(SnGeneratorUtil.getId());
        priceLimit.setPriceLimitLevel(importVO.getPriceLimitLevel().trim());

        // 处理maxPrice字段，从字符串转换为BigDecimal并进行校验
        if (StrUtil.isNotBlank(importVO.getMaxPrice())) {
            String maxPriceStr = importVO.getMaxPrice().trim();
            try {
                // 尝试转换为BigDecimal
                BigDecimal maxPrice = new BigDecimal(maxPriceStr);

                // 校验是否为正数
                if (maxPrice.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new IllegalArgumentException("定价上限金额必须大于0");
                }

                // 校验小数位数是否不超过2位
                if (maxPrice.scale() > 2) {
                    throw new IllegalArgumentException("定价上限金额最多支持2位小数");
                }

                // 设置转换后的值
                priceLimit.setMaxPrice(maxPrice);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("定价上限金额格式不正确，请输入有效的数字");
            }
        }

        return priceLimit;
    }

    /**
     * 查找或创建三目录记录
     * 根据目录编码、项目名称、费用等级、目录类别、开始日期、结束日期这6个字段均相同判断唯一性
     */
    private ThreeCatalogue findOrCreateThreeCatalogue(ThreeCatalogue catalogue) {
        Query query = new Query();

        // 构建查询条件 - 6个字段均相同才认为是同一条记录
        // 1. 目录编码 - 需要考虑null值的情况
        if (StrUtil.isNotBlank(catalogue.getSn())) {
            query.addCriteria(Criteria.where("sn").is(catalogue.getSn()));
        } else {
            query.addCriteria(Criteria.where("sn").is(null));
        }

        // 2. 项目名称
        query.addCriteria(Criteria.where("projectName").is(catalogue.getProjectName()));

        // 3. 费用等级
        query.addCriteria(Criteria.where("level").is(catalogue.getLevel()));

        // 4. 目录类别
        query.addCriteria(Criteria.where("type").is(catalogue.getType()));

        // 5. 开始日期 - 需要考虑null值的情况
        if (catalogue.getStartDate() != null) {
            query.addCriteria(Criteria.where("startDate").is(catalogue.getStartDate()));
        } else {
            query.addCriteria(Criteria.where("startDate").is(null));
        }

        // 6. 结束日期 - 需要考虑null值的情况
        if (catalogue.getEndDate() != null) {
            query.addCriteria(Criteria.where("endDate").is(catalogue.getEndDate()));
        } else {
            query.addCriteria(Criteria.where("endDate").is(null));
        }

        // 查找现有记录
        ThreeCatalogue existing = mongoTemplate.findOne(query, ThreeCatalogue.class);
        if (existing != null) {
            log.debug("找到现有三目录记录，ID: {}, 项目名称: {}", existing.getId(), existing.getProjectName());
            return existing;
        }

        // 创建新记录
        catalogue.setId(SnGeneratorUtil.getId());
        Date now = new Date();
        catalogue.setCreateTime(now);
        catalogue.setUpdateTime(now);

        try {
            mongoTemplate.save(catalogue);
            log.info("成功创建新三目录记录，ID: {}, 项目名称: {}", catalogue.getId(), catalogue.getProjectName());
            return catalogue;
        } catch (Exception e) {
            // 如果保存失败（可能是并发导致的重复），再次查询
            log.warn("保存三目录记录失败，尝试重新查询: {}", e.getMessage());
            ThreeCatalogue retryFind = mongoTemplate.findOne(query, ThreeCatalogue.class);
            if (retryFind != null) {
                return retryFind;
            }
            // 如果还是找不到，抛出异常
            throw new RuntimeException("创建三目录记录失败: " + e.getMessage(), e);
        }
    }
}
