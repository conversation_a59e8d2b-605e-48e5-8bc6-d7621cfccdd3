package com.yixun.wid.controller.admin;

import cn.hutool.core.util.StrUtil;
import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.common.ErrorMessage;
import com.yixun.wid.bean.in.AdminLoginIn;
import com.yixun.wid.bean.in.TwoStepLoginIn;
import com.yixun.wid.bean.other.LoginToken;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.entity.em.CaptchaType;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.exception.ParameterErrorException;
import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.AsyncService;
import com.yixun.wid.service.ShortMsgService;
import com.yixun.wid.utils.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Api(tags = "admin管理员登录V2（两步验证）")
@RestController
@RequestMapping("/v2/admin_login")
public class AdminLoginV2Controller {

    @Value("${spring.profiles.active}")
    private String env;

    @Resource
    private AdministratorService administratorService;

    @Resource
    private AsyncService asyncService;

    @Resource(name = "customRedisTemplate")
    private RedisTemplate redisTemplate;

    @Resource
    private ShortMsgService shortMsgService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @GetMapping("/get_image_code")
    @ApiOperation(value = "登录前获取验证图片")
    public CommonResult getVerifyCode(String mobileCode, HttpServletResponse resp){

        if (mobileCode==null) {
            throw new ParameterErrorException("参数错误");
        }

        //生成随机字串
        String verifyCode = ImageVerifyCodeUtil.generateVerifyCode(4);
        redisTemplate.opsForValue().set(RedisKeyResolver.getAdminCode(mobileCode), verifyCode, 5, TimeUnit.MINUTES);

        //生成图片
        int width = 100;//宽
        int height = 40;//高
        try {
            ImageVerifyCodeUtil.outputImage(width, height, resp.getOutputStream(), verifyCode);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return CommonResult.successResult(null);
    }

    @PostMapping("/pre_login")
    @ApiOperation(value = "第一步：预登录验证（账号密码验证）并发送短信验证码")
    public CommonResult<LoginToken> preLogin(@RequestBody AdminLoginIn adminLoginIn){

        if (!BeanFieldCheckingUtils.forAllFieldNotNull(adminLoginIn)){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        //检查验证码
        String verifyCode = (String) redisTemplate.opsForValue().get(RedisKeyResolver.getAdminCode(adminLoginIn.getMobileCode()));
        if (!adminLoginIn.getVerifyCode().toUpperCase().equals(verifyCode)){
            throw new DataErrorException("验证码错误或已过期");
        }
//        redisTemplate.delete(RedisKeyResolver.getAdminCode(adminLoginIn.getMobileCode()));

        Administrator administrator = administratorService.getAdministratorByName(adminLoginIn.getUsername());
        if (administrator==null){
            throw new DataErrorException("该用户不存在");
        }
        if (administrator.getIsDisable()){
            throw new DataErrorException("该用户已被禁用");
        }

        //5次密码错误锁定10分钟
        LoginLockoutUtil.loginCheck(redisTemplate, adminLoginIn.getUsername(),
                administrator.getPassword().equals(adminLoginIn.getPassword()));

        // 校验用户手机号
        String phone = administrator.getPhone();
        if (phone == null || phone.length() != 11) {
            throw new DataErrorException("用户手机号信息异常，请联系管理员");
        }

        // 检查1分钟内是否有相同的预登录请求，如果有则返回缓存的数据
        String preLoginCacheKey = "PRE_LOGIN_CACHE:" + administrator.getId() + ":" + adminLoginIn.getMobileCode();
        String cachedLoginData = stringRedisTemplate.opsForValue().get(preLoginCacheKey);
        if (cachedLoginData != null) {
            // 解析缓存的数据
            String[] cacheData = cachedLoginData.split("#");
            if (cacheData.length >= 3) {
                LoginToken cachedLoginToken = new LoginToken();
                cachedLoginToken.setToken(cacheData[0]);
                cachedLoginToken.setRandom(cacheData[1]);
                cachedLoginToken.setPhone(cacheData[2]);
                return CommonResult.successData(cachedLoginToken);
            }
        }

        // 账号密码验证通过，生成临时token用于短信验证
        String tempToken = administrator.getToken() + "_temp";
        String random = Base64.encodeBase64String((Math.random() + "").substring(3, 10).getBytes());

        // 存储临时登录信息，用于短信验证后的最终登录
        redisTemplate.opsForValue().set("TEMP_LOGIN:" + tempToken,
                random + "#" + adminLoginIn.getMobileCode() + "#" + administrator.getId() + "#" + phone,
                10, TimeUnit.MINUTES); // 10分钟有效期

        // 直接生成并发送短信验证码
        String smsVerifyCode = VerifyCodeUtils.getCode();

        // bugfix: 生产才发短信
        if ("product".equals(env)
//                || "test".equals(env)
        ) {
            //发送短信
            shortMsgService.sendSMS(AliyunSms.getVerifyCodeMap(smsVerifyCode, phone, AliyunSms.TypeId.SMS_464345447));
        }

        //记录到redis
        stringRedisTemplate.opsForValue().set("CAPTCHA:" + CaptchaType.smsLogin + "_" + phone,
                smsVerifyCode, 65, TimeUnit.SECONDS);

        LoginToken loginToken = new LoginToken();
        loginToken.setToken(tempToken);
        loginToken.setRandom(random);
	    loginToken.setPhone(phone);

        // 缓存本次预登录数据，1分钟内重复请求返回相同数据
        stringRedisTemplate.opsForValue().set(preLoginCacheKey,
                tempToken + "#" + random + "#" + phone, 60, TimeUnit.SECONDS);

        return CommonResult.successData(loginToken);
    }

    @PostMapping("/get_sms_code")
    @ApiOperation(value = "重新获取登录短信验证码（可选，用于重发验证码）")
    public CommonResult<Void> getSmsCode(@RequestBody LoginToken loginToken){

        //检查参数
        if (loginToken.getToken() == null || loginToken.getRandom() == null){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        // 验证临时token
        String tempLoginInfo = (String) redisTemplate.opsForValue().get("TEMP_LOGIN:" + loginToken.getToken());
        if (tempLoginInfo == null) {
            throw new DataErrorException("临时登录信息已过期，请重新登录");
        }

        String[] data = tempLoginInfo.split("#");
        if (data.length < 4 || !data[0].equals(loginToken.getRandom())) {
            throw new DataErrorException("临时登录信息验证失败");
        }

        String phone = data[3];
        if (phone == null || phone.length() != 11) {
            throw new DataErrorException("用户手机号信息异常");
        }

        // 检查是否1分钟内重复请求
        String frequencyKey = "SMS_FREQUENCY:" + phone;
        String lastSendTime = stringRedisTemplate.opsForValue().get(frequencyKey);
        if (lastSendTime != null) {
            throw new DataErrorException("操作过于频繁，请1分钟后再试");
        }

        //生成验证码
        String verifyCode = VerifyCodeUtils.getCode();

        // bugfix: 生产才发短信
        if ("product".equals(env)
//                || "test".equals(env)
        ) {
            //发送短信
            shortMsgService.sendSMS(AliyunSms.getVerifyCodeMap(verifyCode, phone, AliyunSms.TypeId.SMS_464345447));
        }

        //记录到redis
        stringRedisTemplate.opsForValue().set("CAPTCHA:" + CaptchaType.smsLogin + "_" + phone,
                verifyCode, 300, TimeUnit.SECONDS);

        // 设置1分钟内不能重复发送的限制
        stringRedisTemplate.opsForValue().set(frequencyKey, "1", 60, TimeUnit.SECONDS);

        return CommonResult.successResult("短信发送成功");
    }

    @PostMapping("/login")
    @ApiOperation(value = "第二步：完成登录（短信验证码验证）")
    public CommonResult<LoginToken> login(@RequestBody TwoStepLoginIn twoStepLoginIn){

        //检查参数
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(twoStepLoginIn)){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        // 验证临时token
        String tempLoginInfo = (String) redisTemplate.opsForValue().get("TEMP_LOGIN:" + twoStepLoginIn.getTempToken());
        if (tempLoginInfo == null) {
            throw new DataErrorException("临时登录信息已过期，请重新登录");
        }

        String[] data = tempLoginInfo.split("#");
        if (data.length < 4 || !data[0].equals(twoStepLoginIn.getRandom())) {
            throw new DataErrorException("临时登录信息验证失败");
        }

        String random = data[0];
        String mobileCode = data[1];
        Long administratorId = Long.parseLong(data[2]);
        String phone = data[3];

        if (phone.length() != 11) {
            throw new ParameterErrorException(ErrorMessage.phone_error);
        }

        //检查验证码
        VerifyCodeCheckUtil.checkVerifyCode(stringRedisTemplate, phone, twoStepLoginIn.getVerifyCode(),
                CaptchaType.smsLogin);

        //用户检查
        Administrator administrator = administratorService.getAdministratorById(administratorId);
        if (administrator == null) {
            throw new DataErrorException(ErrorMessage.account_not_exist);
        }
        if (administrator.getIsDisable()) {
            throw new ParameterErrorException(ErrorMessage.account_is_disabled);
        }

        // 删除临时登录信息
        redisTemplate.delete("TEMP_LOGIN:" + twoStepLoginIn.getTempToken());

        // 生成正式登录token
        LoginToken loginToken = new LoginToken();
        loginToken.setToken(administrator.getToken());
        String newRandom = Base64.encodeBase64String((Math.random() + "").substring(3, 10).getBytes());
        loginToken.setRandom(newRandom);

        redisTemplate.opsForValue().set(RedisKeyResolver.getAdminUserToken(administrator.getToken()),
                newRandom + "#" + mobileCode + "#" + administrator.getId(),
                60, TimeUnit.MINUTES);

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String realIP = getRealIP(request);
        administrator.setLastLoginIp(realIP);
        administrator.setLastLoginTime(new Date());
        administratorService.update(administrator);

        asyncService.recordLogLogin("/v2/admin_login/login", administrator.getUsername(), administrator.getId(),
                mobileCode, realIP);

        if (StrUtil.isNotBlank(twoStepLoginIn.getMobileCode())) {
            redisTemplate.delete(RedisKeyResolver.getAdminCode(twoStepLoginIn.getMobileCode()));
        }

        return CommonResult.successData(loginToken);
    }

    @PostMapping("/logout")
    @ApiOperation(value = "退出登录")
    public CommonResult<Void> logout(){

        Long userId = AdminUserHelper.getCurrentUserId();
        Administrator administrator = administratorService.getAdministratorById(userId);
        redisTemplate.delete(RedisKeyResolver.getAdminUserToken(administrator.getToken()));

        return CommonResult.successResult(null);
    }

    String getRealIP(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
