package com.yixun.wid.v2.vo.ai;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 清单OCR响应结果
 */
@Data
public class ListOcrResponse {

    /**
     * 状态
     */
    private String status;

    /**
     * 数据
     */
    private ListOcrData data;

    /**
     * 消息
     */
    private String message;

    @Data
    public static class ListOcrData {
        /**
         * 费用清单
         */
        @JSONField(name = "expense_list")
        private List<ExpenseListItem> expenseList;
    }

    @Data
    public static class ExpenseListItem {
        /**
         * 费用名称
         */
        private String name;

        /**
         * 费用项目的类别(治疗费，检查治疗费，药品费，材料费，血费，床位费，未匹配)
         */
        @JSONField(name = "expense_item")
        private String expenseItem;

        /**
         * 项目编码
         */
        private String code;

        /**
         * 数量
         */
        private Integer quantity;

        /**
         * 单价
         */
        @JSONField(name = "unit_price")
        private BigDecimal unitPrice;

        /**
         * 金额
         */
        private BigDecimal amount;
    }
}
