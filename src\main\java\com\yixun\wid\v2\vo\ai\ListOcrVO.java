package com.yixun.wid.v2.vo.ai;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 清单OCR识别响应VO
 * 用于返回AI识别的清单信息数据，不直接返回完整的ListOcrResponse对象
 */
@Data
public class ListOcrVO {

    /**
     * 文件URL
     */
    private String fileUrl;

    /**
     * 账单明细列表
     */
    private List<BillingDetailItem> billingDetails;

    @Data
    public static class BillingDetailItem {
        /**
         * 明细在当前账单中的序号
         */
        private Integer orderNum;

        /**
         * 项目名称
         */
        private String projectName;

        /**
         * 项目编码
         */
        private String projectCode;

        /**
         * 费用类别
         */
        private String feeType;

        /**
         * 费用分类
         */
        private String feeCategory;

        /**
         * 费用等级
         */
        private String feeLevel;

        /**
         * 数量
         */
        private Integer quantity;

        /**
         * 单价
         */
        private BigDecimal unitPrice;

        /**
         * 金额
         */
        private BigDecimal amount;

        /**
         * 不可报销金额
         */
        private BigDecimal nonReimbursableAmount;
    }
}
