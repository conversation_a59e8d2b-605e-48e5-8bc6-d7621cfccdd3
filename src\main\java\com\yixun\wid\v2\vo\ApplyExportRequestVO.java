package com.yixun.wid.v2.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 申请记录导出请求参数
 */
@Data
public class ApplyExportRequestVO {

    @NotNull(message = "用人单位ID不能为空")
    @ApiModelProperty("用人单位ID，必填")
    private Long organizationId;

	/**
	 * 用人单位名称
	 */
	@NotNull(message = "用人单位名称不能为空")
	private String organizationName;

    @ApiModelProperty("业务类型，默认'工伤认定申请'，可选择'工伤事故报备'")
    private String businessType = "工伤认定申请";

    @ApiModelProperty("时间范围，1-12整数，小于1则为1，大于12则为12，数字代表导出的月份，1为近1个月，2为近2个月以此类推")
    private Integer timeRange = 1;
}
