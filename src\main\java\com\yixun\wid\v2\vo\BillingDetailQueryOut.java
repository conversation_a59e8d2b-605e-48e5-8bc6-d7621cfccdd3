package com.yixun.wid.v2.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 账单明细查询输出结果
 */
@Data
public class BillingDetailQueryOut {

    /**
     * 账单总金额 （发票金额）
     */
    private BigDecimal totalAmount;

    /**
     * 账单明细总金额 （电子清单金额）
     */
    private BigDecimal billingDetailsTotalAmount;

    /**
     * 账单明细分组列表 （发票金额明细）
     */
    private List<BillingDetailsGroupItem> billingDetailsGroups;

    /**
     * 基于费用类别的金额统计 （电子清单明细）
     */
    private List<FeeTypeStatisticItem> feeTypeStatistics;
    
    /**
     * 账单明细统计信息（使用feeType筛选）
     */
    private BillingDetailStatistics billingDetailStatistics;

    /**
     * 账单明细分组项
     */
    @Data
    public static class BillingDetailsGroupItem {

        /**
         * 费用类别
         */
        @ApiModelProperty(value = "费用类别")
        private String feeType;

        /**
         * 账单金额
         */
        @ApiModelProperty(value = "账单金额")
        private BigDecimal billAmount;
    }

    /**
     * 费用类别统计项
     */
    @Data
    public static class FeeTypeStatisticItem {

        /**
         * 费用类别
         */
        @ApiModelProperty(value = "费用类别")
        private String feeType;

        /**
         * 该费用类别的总金额
         */
        @ApiModelProperty(value = "该费用类别的总金额")
        private BigDecimal totalAmount;
    }
}
