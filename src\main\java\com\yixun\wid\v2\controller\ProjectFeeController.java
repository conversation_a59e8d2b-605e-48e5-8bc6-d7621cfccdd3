package com.yixun.wid.v2.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.mongodb.client.result.DeleteResult;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.bean.in.ProjectFeeBatchDeleteIn;
import com.yixun.wid.v2.bean.in.ProjectFeeIn;
import com.yixun.wid.v2.bean.in.ProjectFeeSimilarSearchIn;
import com.yixun.wid.v2.entity.ProjectFee;
import com.yixun.wid.v2.service.ProjectFeeService;
import com.yixun.wid.v2.utils.AiUtils;
import com.yixun.wid.v2.vo.medical.SimilarInstitutionResult;
import com.yixun.wid.v2.vo.ai.SimilarityResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 待遇 项目费用管理相关接口
 */
@RestController
@RequestMapping("/v2/projectfee")
public class ProjectFeeController {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private AiUtils aiUtils;

    @Autowired
    private ProjectFeeService projectFeeService;

    /**
     * 分页查询项目费用列表
     * @param projectName 项目名称（模糊查询）
     * @param feeType 费用类型
     * @param hospitalId 医疗机构ID
     * @param commonPage 分页参数
     * @return 分页结果
     */
    @GetMapping("/list")
    public CommonResult<List<ProjectFee>> list(
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String feeType,
            @RequestParam(required = false) Long hospitalId,
            CommonPage commonPage) {
        // 构建查询条件
        Query query = new Query();

        // 项目名称模糊查询
        if (StrUtil.isNotBlank(projectName)) {
            query.addCriteria(Criteria.where("projectName").regex(".*" + projectName + ".*", "i"));
        }

        // 费用类型精确匹配
        if (StrUtil.isNotBlank(feeType)) {
            query.addCriteria(Criteria.where("feeType").is(feeType));
        }
        
        // 医疗机构ID精确匹配
        if (hospitalId != null) {
            query.addCriteria(Criteria.where("hospitalId").is(hospitalId));
        }

        // 添加排序：按创建时间倒序
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));

        // 设置分页信息
        MongoUtil.setPageInfo(mongoTemplate, ProjectFee.class, query, commonPage);

        // 执行查询
        List<ProjectFee> projectFees = mongoTemplate.find(query, ProjectFee.class);

        return CommonResult.successPageData(projectFees, commonPage);
    }

    /**
     * 根据ID获取项目费用详情
     * @param id 项目费用ID
     * @return 项目费用详情
     */
    @GetMapping("/detail")
    public CommonResult<ProjectFee> getDetail(@RequestParam Long id) {
        ProjectFee projectFee = mongoTemplate.findById(id, ProjectFee.class);
        if (projectFee == null) {
            return CommonResult.failResult(10001, "项目费用不存在");
        }
        return CommonResult.successData(projectFee);
    }

    /**
     * 新增项目费用
     * @param projectFee 项目费用信息
     * @return 新增结果
     */
    @PostMapping("/add")
    public CommonResult<ProjectFee> add(@RequestBody ProjectFee projectFee) {
        // 生成唯一ID
        projectFee.setId(SnGeneratorUtil.getId());

        // 设置创建时间和更新时间
        Date now = new Date();
        projectFee.setCreateTime(now);
        projectFee.setUpdateTime(now);

        // 保存到数据库
        mongoTemplate.save(projectFee);

        return CommonResult.successData(projectFee);
    }

    /**
     * 更新项目费用
     * @param projectFee 项目费用信息
     * @return 更新结果
     */
    @PostMapping("/update")
    public CommonResult<ProjectFee> update(@RequestBody ProjectFee projectFee) {
        // 检查ID是否存在
        if (projectFee.getId() == null) {
            return CommonResult.failResult(10001, "项目费用ID不能为空");
        }

        // 查询原有数据
        ProjectFee existingFee = mongoTemplate.findById(projectFee.getId(), ProjectFee.class);
        if (existingFee == null) {
            return CommonResult.failResult(10001, "项目费用不存在");
        }

        // 设置更新时间
        projectFee.setUpdateTime(new Date());
        // 保留创建时间
        projectFee.setCreateTime(existingFee.getCreateTime());

        // 更新到数据库
        mongoTemplate.save(projectFee);

        return CommonResult.successData(projectFee);
    }

    /**
     * 批量删除项目费用
     * @param in 批量删除参数
     * @return 删除结果
     */
    @PostMapping("/batchDelete")
    public CommonResult<Long> batchDelete(@RequestBody ProjectFeeBatchDeleteIn in) {
        // 检查ID列表是否为空
        if (in.getIds() == null || in.getIds().isEmpty()) {
            return CommonResult.failResult(10001, "项目费用ID列表不能为空");
        }

        // 构建查询条件
        Query query = new Query(Criteria.where("_id").in(in.getIds()));

        // 执行删除
        DeleteResult result = mongoTemplate.remove(query, ProjectFee.class);

        return CommonResult.successData(result.getDeletedCount());
    }

    /**
     * 根据项目名称搜索相似项目
     *
     * @param projectName 项目名称
     * @param num 返回结果数量
     * @return 相似项目列表
     */
    @GetMapping("/searchSimilarProjects")
    public CommonResult<List<ProjectFee>> searchSimilarProjects(
            @RequestParam String projectName,
            @RequestParam(required = false, defaultValue = "5") Integer num,
            @RequestParam(required = false, defaultValue = "0.8") Double similarityThreshold) {

        // 检查项目名称是否为空
        if (StrUtil.isBlank(projectName)) {
            return CommonResult.failResult(10001, "项目名称不能为空");
        }

        // 查询数据库中所有项目名称（排除当前搜索的项目名称）
        Query query = new Query();
        query.addCriteria(Criteria.where("projectName").ne(projectName)); // 排除当前搜索的项目名称
        query.fields().include("projectName"); // 只查询projectName字段

        List<ProjectFee> allProjects = mongoTemplate.find(query, ProjectFee.class);
        List<String> targets = allProjects.stream()
                .map(ProjectFee::getProjectName)
                .filter(StrUtil::isNotBlank)
                .distinct() // 去重
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(targets)) {
            // 如果没有其他项目，返回空结果
            return CommonResult.successData(new ArrayList<>());
        }

        // 使用AI工具查询相似项目
        List<SimilarityResult> similarResults = aiUtils.findSimilarItems(projectName, targets, num, similarityThreshold);

        if (CollUtil.isEmpty(similarResults)) {
            return CommonResult.successData(new ArrayList<>());
        }

        // 提取相似项目的名称
        List<String> similarNames = similarResults.stream()
            .map(SimilarityResult::getName)
            .collect(Collectors.toList());

        // 根据名称查询完整的项目费用信息
        Query detailQuery = new Query(Criteria.where("projectName").in(similarNames));
        List<ProjectFee> similarProjects = mongoTemplate.find(detailQuery, ProjectFee.class);

        // 为了保持AI返回的相似度顺序，我们需要对查询结果进行排序
        java.util.Map<String, ProjectFee> projectMap = similarProjects.stream()
            .collect(Collectors.toMap(ProjectFee::getProjectName, p -> p, (p1, p2) -> p1)); // handle duplicates

        List<ProjectFee> sortedProjects = similarNames.stream()
            .map(projectMap::get)
            .filter(java.util.Objects::nonNull)
            .collect(Collectors.toList());

        return CommonResult.successData(sortedProjects);
    }

    /**
     * 项目费用相似搜索接口
     * 基于AiUtils中的相似查找功能，实现项目费用的智能匹配
     *
     * @param searchIn 相似搜索参数
     * @return 相似度最高的一条项目费用数据的完整详情
     */
    @PostMapping("/similarSearch")
    public CommonResult<ProjectFee> similarSearch(@Validated @RequestBody ProjectFeeSimilarSearchIn searchIn) {
        ProjectFee result = projectFeeService.similarSearch(searchIn);

        if (result == null) {
            return CommonResult.failResult(10001, "未找到匹配的项目费用数据");
        }

        return CommonResult.successData(result);
    }
}
