package com.yixun.wid.controller.admin;

import com.yixun.bean.CommonResult;
import com.yixun.wid.bean.common.ErrorMessage;
import com.yixun.wid.bean.in.AdminLoginIn;
import com.yixun.wid.bean.in.SMSCodeIn;
import com.yixun.wid.bean.in.SMSLoginIn;
import com.yixun.wid.bean.other.LoginToken;
import com.yixun.wid.entity.Administrator;
import com.yixun.wid.entity.User;
import com.yixun.wid.entity.em.CaptchaType;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.exception.ParameterErrorException;
import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.AsyncService;
import com.yixun.wid.service.ShortMsgService;
import com.yixun.wid.utils.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Api(tags = "admin管理员登录")
@RestController
@RequestMapping("/admin_login")
public class AdminLoginController {


	@Value("${spring.profiles.active}")
	private String env;


	@Resource
    private AdministratorService administratorService;

    @Resource
    private AsyncService asyncService;

    @Resource(name = "customRedisTemplate")
    private RedisTemplate redisTemplate;


	@Resource
	private ShortMsgService shortMsgService;


	@Resource
	private StringRedisTemplate stringRedisTemplate;

//
//	@GetMapping("/get_sms_code")
//	@ApiOperation(value = "获取短信")
//	public CommonResult<Void> getSmsCode(SMSCodeIn smsCodeIn){
//
//		//检查参数
//		if (!BeanFieldCheckingUtils.forAllFieldNotNull(smsCodeIn)){
//			throw new ParameterErrorException(ErrorMessage.parameter_error);
//		}
//		if (smsCodeIn.getPhone().length()!=11){
//			throw new ParameterErrorException(ErrorMessage.phone_error);
//		}
//
////		if (smsCodeIn.getCaptchaType().equals(CaptchaType.smsLogin)){
//			Administrator user = administratorService.getByPhoneType(smsCodeIn.getPhone(), smsCodeIn.getAdministratorType().name());
//			if (user ==null){
//				throw new DataErrorException("该手机号还没有注册。");
//			}
////		}
//
//		//生成验证码
//		String verifyCode = VerifyCodeUtils.getCode();
//
//		// bugfix: 生产才发短信
//		if ("product".equals(env)) {
//			//发送短信
//			shortMsgService.sendSMS(AliyunSms.getVerifyCodeMap(verifyCode, smsCodeIn.getPhone(), AliyunSms.TypeId.SMS_464345447));
//		}
//
//		//记录到redis
//		stringRedisTemplate.opsForValue().set("CAPTCHA:" + CaptchaType.smsLogin + "_" + smsCodeIn.getPhone(),
//			verifyCode, 300, TimeUnit.SECONDS);
//
//		return CommonResult.successResult("短信发送成功");
//	}
//
//	@PostMapping("/sms_login")
//	@ApiOperation(value = "短信登录")
//	public CommonResult<LoginToken> smsLogin(@RequestBody SMSLoginIn loginIn){
//
//		//检查参数
//		if (!BeanFieldCheckingUtils.forAllFieldNotNull(loginIn)){
//			throw new ParameterErrorException(ErrorMessage.parameter_error);
//		}
//		if (loginIn.getPhone().length()!=11){
//			throw new ParameterErrorException(ErrorMessage.phone_error);
//		}
//
//		//检查验证码
//		VerifyCodeCheckUtil.checkVerifyCode(stringRedisTemplate, loginIn.getPhone(), loginIn.getVerifyCode(),
//			CaptchaType.smsLogin);
//
//		//用户检查
//		Administrator user = administratorService.getByPhoneType(loginIn.getPhone(), loginIn.getAdministratorType().name());
//		if (user ==null){
//			throw new DataErrorException(ErrorMessage.account_not_exist);
//		}else {
//			if (user.getIsDisable()){
//				throw new ParameterErrorException(ErrorMessage.account_is_disabled);
//			}
//			if (!user.getType().equals(loginIn.getAdministratorType().name())){
//				throw new DataErrorException("账号类型错误");
//			}
//		}
//
////		setUserInfoCache(user);
//
////		recordLogin(user, "短信登录");
//
//		//设置当前登录token
////		LoginToken loginToken = getLoginToken(user.getId(), loginIn.getClientId(), loginIn.getLoginType());
//
//		return CommonResult.successData(new LoginToken());
//	}
//
//    @GetMapping("/get_image_code")
//    @ApiOperation(value = "登录前获取验证图片")
//    public CommonResult getVerifyCode(String mobileCode, HttpServletResponse resp){
//
//        if (mobileCode==null) {
//	        throw new ParameterErrorException("参数错误");
//        }
//
//        //生成随机字串
//        String verifyCode = ImageVerifyCodeUtil.generateVerifyCode(4);
//        redisTemplate.opsForValue().set(RedisKeyResolver.getAdminCode(mobileCode), verifyCode, 5, TimeUnit.MINUTES);
//
//        //生成图片
//        int width = 100;//宽
//        int height = 40;//高
//        try {
//            ImageVerifyCodeUtil.outputImage(width, height, resp.getOutputStream(), verifyCode);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//
//        return CommonResult.successResult(null);
//    }

    @PostMapping("/login")
    @ApiOperation(value = "系统登录")
    public CommonResult<LoginToken> login(@RequestBody AdminLoginIn adminLoginIn){
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(adminLoginIn)){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        //检查验证码
        String verifyCode = (String) redisTemplate.opsForValue().get(RedisKeyResolver.getAdminCode(adminLoginIn.getMobileCode()));
        if (!adminLoginIn.getVerifyCode().toUpperCase().equals(verifyCode)){
            throw new DataErrorException("验证码错误或已过期");
        }
        redisTemplate.delete(RedisKeyResolver.getAdminCode(adminLoginIn.getMobileCode()));

        Administrator administrator = administratorService.getAdministratorByName(adminLoginIn.getUsername());
        if (administrator==null){
            throw new DataErrorException("该用户不存在");
        }
        if (administrator.getIsDisable()){
            throw new DataErrorException("该用户已被禁用");
        }

        //5次密码错误锁定10分钟
        LoginLockoutUtil.loginCheck(redisTemplate, adminLoginIn.getUsername(),
                administrator.getPassword().equals(adminLoginIn.getPassword()));

        LoginToken loginToken = new LoginToken();
        loginToken.setToken(administrator.getToken());
        String random = Base64.encodeBase64String((Math.random() + "").substring(3, 10).getBytes());
        loginToken.setRandom(random);

        redisTemplate.opsForValue().set(RedisKeyResolver.getAdminUserToken(administrator.getToken()),
                random + "#" + adminLoginIn.getMobileCode()+ "#" + administrator.getId(),
                60, TimeUnit.MINUTES);

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String realIP = getRealIP(request);
        administrator.setLastLoginIp(realIP);
        administrator.setLastLoginTime(new Date());
        administratorService.update(administrator);

        asyncService.recordLogLogin("/admin_login/login", administrator.getUsername(), administrator.getId(),
                adminLoginIn.getMobileCode(), realIP);

        return CommonResult.successData(loginToken);
    }

    @PostMapping("/logout")
    @ApiOperation(value = "退出登录")
    public CommonResult<Void> logout(){

        Long userId = AdminUserHelper.getCurrentUserId();
        Administrator administrator = administratorService.getAdministratorById(userId);
        redisTemplate.delete(RedisKeyResolver.getAdminUserToken(administrator.getToken()));

        return CommonResult.successResult(null);
    }

    String getRealIP(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
